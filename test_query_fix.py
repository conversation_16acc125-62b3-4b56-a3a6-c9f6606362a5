#!/usr/bin/env python3
"""
Test script to verify that queries are sent without prefix for better similarity matching
"""

import requests
import json

def test_query_without_prefix():
    """Test that user queries are sent without 'Question about EoDB services:' prefix"""
    
    base_url = "http://localhost:8022"
    
    # Step 1: Create session
    print("=== Step 1: Creating session ===")
    session_response = requests.post(f"{base_url}/session_manager", json={
        "session_type": "session_create",
        "session_id": ""
    })
    session_data = session_response.json()
    session_id = session_data["session_id"]
    print(f"Session ID: {session_id}")
    
    # Step 2-4: Quick setup to get to query mode
    print("\n=== Setting up to query mode ===")
    
    # Sector
    requests.post(f"{base_url}/chatbot/step", json={
        "session_id": session_id,
        "step": 2,
        "user_response": {"caption": "sector", "value": "Others"},
        "user_input": ""
    })
    
    # Investment
    requests.post(f"{base_url}/chatbot/step", json={
        "session_id": session_id,
        "step": 3,
        "user_response": {"caption": "investment", "value": "Less than INR 2.5 Cr."},
        "user_input": ""
    })
    
    # Choose general queries
    requests.post(f"{base_url}/chatbot/step", json={
        "session_id": session_id,
        "step": 4,
        "user_response": {"caption": "application_type", "value": "Do you want to ask general queries?"},
        "user_input": ""
    })
    
    # Step 5: Ask a question that should get a good match
    print("\n=== Step 5: Ask 'what is silpasathi prakalpa?' ===")
    print("🔍 Testing: Query should be sent without prefix for better similarity matching")
    
    query_response = requests.post(f"{base_url}/chatbot/step", json={
        "session_id": session_id,
        "step": 4,
        "user_response": {"caption": "general_query", "value": ""},
        "user_input": "what is silpasathi prakalpa?",
        "response_type": "",
        "followup_yes": "",
        "followup_no": ""
    })
    
    response_data = query_response.json()
    print(f"Intent: {response_data.get('intent_name')}")
    print(f"Response: {response_data.get('response', 'No response')[:200]}...")
    
    # Check if we got a proper AI response (not fallback)
    if response_data.get("intent_name") == "ai_response" and "silpasathi" in response_data.get("response", "").lower():
        print("✅ SUCCESS: Got direct AI response from knowledge base!")
        print("✅ FIX WORKING: Query sent without prefix, better similarity matching!")
        return True
    elif response_data.get("intent_name") == "ai_response":
        print("✅ PARTIAL SUCCESS: Got AI response, but check if it's about Silpasathi")
        print(f"Response content: {response_data.get('response', '')}")
        return True
    else:
        print("❌ STILL GOING TO RAG: Query might still have issues")
        print(f"Intent: {response_data.get('intent_name')}")
        return False

if __name__ == "__main__":
    try:
        success = test_query_without_prefix()
        if success:
            print("\n🎉 QUERY PREFIX FIX SUCCESSFUL!")
        else:
            print("\n❌ QUERY PREFIX FIX NEEDS MORE WORK!")
    except Exception as e:
        print(f"Error: {e}")
