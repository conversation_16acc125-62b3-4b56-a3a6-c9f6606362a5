from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import logging
import uvicorn
import json
import psycopg2
import pandas as pd
import numpy as np
import re
import uuid
import datetime
from pytz import timezone
from pymilvus import utility
from chatbot_implementation import (
    connect_to_milvus,
    create_collection_and_load_data,
    chatbot_response as ai_chatbot_response,
    get_collections,
    model  # Import the global model instance
)
import requests
from fastapi.responses import JSONResponse

# --- Logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- FastAPI App ---
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Timezone ---
tz = timezone('Asia/Kolkata')
def json_load_rag()->list:
    with open("rag_link_2.json", "r") as f:
        data = json.load(f)
    return data

def rag_api(link_data, question):
    url = "https://wb-gateway.napix.gov.in/wb/coeailabkol/web_rag"

    payload = json.dumps({
        "params": {
            "key": "Ailab@123",
            "job_id": "5",
            "task_id":"1",
            "input_lst": link_data,
            "qus_str": question,
            "no_of_ques": "NA"
            }
            }
            )
    headers = {
    'client_id': '7d84dff9e324f19a65ebf32c9fe0030a',
    'client_secret': '57ec1c37c60454b31f5494913a048d81',
    'Content-Type': 'application/json'
    }
    r = requests.request("POST", url, headers=headers, data=payload) #change the api endpoint
    print(r.status_code)
    if r.status_code == 200:
        json_res=json.loads(r.content.decode('utf-8'))
    else:
        json_res= {'status': 'success', 'message': 'No response found', 'job_id': '5', 'question': [], 'answer': ["No response found at this moment please try again later."], 'urls': ['https://example.com'], 'discarded_urls': []}

    return json_res


# --- PostgreSQL Connection ---
def get_pg_conn():
    return psycopg2.connect(
        database="user_session_eodb_data_DB",
        user="postgres",
        password="padmin",
        host="***********",
        port="5432"
    )

# --- Session DB Operations ---
def store_session(session_id, sector, investment):
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        # Create table if it doesn't exist
        cur.execute("""
            CREATE TABLE IF NOT EXISTS user_session_eodbh_data_tab (
                session_id VARCHAR PRIMARY KEY,
                sector VARCHAR,
                investment VARCHAR,
                selected_service VARCHAR,
                time TIMESTAMP DEFAULT NOW()
            );
        """)

        # Add selected_service column if it doesn't exist (for existing tables)
        try:
            cur.execute("""
                ALTER TABLE user_session_eodbh_data_tab
                ADD COLUMN IF NOT EXISTS selected_service VARCHAR;
            """)
        except Exception as alter_error:
            # Column might already exist, that's fine
            logger.info(f"Column selected_service might already exist: {alter_error}")

        # Insert or update session data
        cur.execute("""
            INSERT INTO user_session_eodbh_data_tab (session_id, sector, investment, time)
            VALUES (%s, %s, %s, NOW())
            ON CONFLICT (session_id) DO UPDATE SET sector=EXCLUDED.sector, investment=EXCLUDED.investment, time=NOW();
        """, (session_id, sector, investment))
        conn.commit()
    except Exception as e:
        logger.error(f"PostgreSQL error: {e}")
    finally:
        conn.close()

def store_selected_service(session_id, service):
    """Store selected service in session data"""
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        # Update the selected_service for existing session
        cur.execute("""
            UPDATE user_session_eodbh_data_tab
            SET selected_service = %s, time = NOW()
            WHERE session_id = %s
        """, (service, session_id))

        # If no rows were updated, insert a new record
        if cur.rowcount == 0:
            cur.execute("""
                INSERT INTO user_session_eodbh_data_tab (session_id, selected_service, time)
                VALUES (%s, %s, NOW())
            """, (session_id, service))

        conn.commit()
    except Exception as e:
        logger.error(f"PostgreSQL error storing selected service: {e}")
    finally:
        conn.close()

def get_session(session_id):
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        cur.execute("""
            SELECT sector, investment, selected_service FROM user_session_eodbh_data_tab
            WHERE session_id = %s
        """, (session_id,))
        result = cur.fetchone()
        if result:
            return {"sector": result[0], "investment": result[1], "selected_service": result[2]}
        return None
    except Exception as e:
        logger.error(f"PostgreSQL error: {e}")
        return None
    finally:
        conn.close()

def get_selected_service(session_id):
    """Get selected service from session data"""
    session_data = get_session(session_id)
    if session_data and session_data.get("selected_service"):
        return session_data["selected_service"]
    return None

##--- Knowledgebase Embedding & Search ---
class KnowledgeBase:
    def __init__(self, csv_path):
        self.df = pd.read_csv(csv_path)
        self.embeddings = model.encode(self.df['response'].astype(str).tolist(), show_progress_bar=True)

    def search(self, query, top_k=1):
        query_emb = model.encode([query])[0]
        scores = np.dot(self.embeddings, query_emb) / (np.linalg.norm(self.embeddings, axis=1) * np.linalg.norm(query_emb))
        valid_indices = np.where(~np.isnan(scores))[0]
        if len(valid_indices) == 0:
            return []
        sorted_indices = valid_indices[np.argsort(scores[valid_indices])[::-1][:top_k]]
        # Only use indices that are within DataFrame bounds
        sorted_indices = [i for i in sorted_indices if 0 <= i < len(self.df)]
        if not sorted_indices:
            return []
        result_df = self.df.iloc[sorted_indices].replace([np.inf, -np.inf], np.nan).where(pd.notnull(self.df), None)
        return result_df.to_dict(orient='records')

kb = KnowledgeBase("eodb_silpasathi_knowledgebase_03_06_25.csv")

##--- API Models ---
class UserResponse(BaseModel):
    caption: str
    value: str

class ChatRequest(BaseModel):
    session_id: Optional[str] = None
    collection_name: Optional[str] = None  # Made optional for stepwise flow
    user_input: Optional[str] = ""  # Made optional for stepwise flow
    step: Optional[int] = 1
    user_response: Optional[UserResponse] = None
    response_type: Optional[str] = None
    followup_yes: Optional[str] = None
    followup_no: Optional[str] = None
    criteria_list: Optional[List] = []

class PredictParams(BaseModel):
    user_input: str
    collection_name: str
    followup_yes: Optional[str] = None
    followup_no: Optional[str] = None

class CreateCollectionRequest(BaseModel):
    csv_path: str

class SessionIdGenerator(BaseModel):
    session_type: str
    session_id: Optional[str] = None

##--- Stepwise Options ---
SECTORS = [
    "Agriculture & Cooperation",
    "Animal Husbandry & Fishing",
    "Art & Culture",
    "Chemicals & Fertilizers",
    "Coal & Mine",
    "Commerce & Industry",
    "Communications & Information Technology",
    "Defence",
    "Education & Training",
    "Employment & Labour",
    "Energy & Power",
    "Environment & Natural Resources",
    "Finance, Banking & Insurance",
    "Food & Public Distribution",
    "Forestry & Wildlife",
    "Governance & Administration",
    "Health & Family Welfare",
    "Home Affairs & National Security",
    "Housing & Urban Development",
    "Information & Broadcasting",
    "International Affairs",
    "Law & Justice",
    "People & Organisations",
    "Petroleum, Oil & Natural Gas",
    "Rural Development & Panchayati Raj",
    "Science, Technology & Research",
    "Social Justice & Empowerment",
    "Tourism",
    "Transport & Infrastructure",
    "Youth Affairs & Sports",
    "Others"
]

INVESTMENTS = [
    "Less than INR 2.5 Cr.", "Between INR 2.5 Cr to 25 Cr.", "Between INR 25 Cr. To INR 125 Cr.", "More than INR 125 Cr.", "Others"
]

PRE_ESTABLISHMENT = [
    "Conversion of Land Use", "Building Plan Approval (WBIDC)", "Building Plan Approval (WBIIDC)", "Building Plan Approval (WBEIDC)", "Fire Safety Recommendation", "Allocation/Permission for Surface Water if Surface Water is the Source", "Land Allocation (IDC)", "Consent to Establish under Water and Air Act", "Tree Felling & Tree Transit Permission", "Approval of Factory Plan under the Factories Act 1948", "Mutation of Land", "Registration of Property", "Drug License (Retail)", "Drug License (Wholesale)", "Temporary Electricity Connection (WBSEDCL)", "Permission for Extraction of Ground Water"
]

PRE_OPERATION = [
    "Building Occupancy Certificate", "NOC (Fire Safety Certificate) from Fire & ES Dept.", "Fire License", "Trade License", "Consent to Operate under Water and Air Act", "Electricity Connection", "Registration under Boiler Act, 1923", "Factory License under Factories Act 1948", "Registration under The Shops and Establishment Act", "Profession Tax Registration", "ESI Registration for Employer", "EPF Registration", "TAN", "GST"
]

# --- Helper Functions ---
def get_or_create_collection():
    try:
        connect_to_milvus()
        collections = utility.list_collections()

        if collections:
            sorted_collections = sorted(collections)
            collection_name = sorted_collections[-1]
            logger.info(f"Using existing collection: {collection_name}")
        else:
            logger.info("No collections found. Creating a new collection.")
            csv_path = "eodb_silpasathi_knowledgebase_03_06_25.csv"
            collection_name = create_collection_and_load_data(csv_path)
            if collection_name:
                logger.info(f"Created new collection: {collection_name}")
            else:
                logger.warning("Failed to create collection")
                return None

        return collection_name
    except Exception as e:
        logger.error(f"Error in get_or_create_collection: {e}")
        return None

def format_options_as_buttons(options_list):
    """Convert a list of options to HTML button elements"""
    button_list = []
    for option in options_list:
        # Create a safe value for the button ID (remove spaces, special chars)
        safe_id = re.sub(r'[^a-zA-Z0-9]', '_', option.lower())
        button_list.append(f"<button type='button' id='{safe_id}_btn' class='option_btn {safe_id}_btn' value='{option}'>{option}</button>")
    return button_list

# --- Chatbot Endpoints ---
@app.post("/chatbot/step")
async def chatbot_step(req: ChatRequest):
    """
    EoDB chatbot
    """
    try:
        # Generate session_id if not provided
        if not req.session_id:
            req.session_id = str(uuid.uuid4())

        # Extract values from user_response if provided and store in session
        if req.user_response:
            caption = req.user_response.caption.lower()
            value = req.user_response.value

            # Store the current step's data in session
            if caption == "sector":
                store_session(req.session_id, value, "")
            elif caption == "investment":
                # Get existing sector from session and update with investment
                existing_data = get_session(req.session_id)
                if existing_data:
                    store_session(req.session_id, existing_data.get('sector', ''), value)
                else:
                    store_session(req.session_id, "", value)

        # Get current session data
        session_data = get_session(req.session_id)
        sector = session_data.get('sector', '') if session_data else None
        investment = session_data.get('investment', '') if session_data else None

        # Extract application_type, service_type, and selected_service from current request
        application_type = None
        service_type = None
        selected_service = None
        if req.user_response:
            caption = req.user_response.caption.lower()
            value = req.user_response.value
            if caption == "application_type":
                application_type = value
            elif caption == "service_type":
                service_type = value
            elif caption == "selected_service":
                selected_service = value
                # Store selected service in session for context in future queries
                store_selected_service(req.session_id, value)

        # Determine response_type if not provided
        if not req.response_type:
            # Check if user is in general query mode (step 4 with user_input or general_query caption)
            if req.step == 4 and ((req.user_response and req.user_response.caption.lower() == "general_query") or
                                  (req.user_input and req.user_input.strip()) or
                                  (application_type == "Do you want to ask general queries?")):
                req.response_type = "text"
            # Step 6 is for service-specific queries (text input)
            elif req.step == 6 and ((req.user_response and req.user_response.caption.lower() == "service_query") or
                                    (req.user_input and req.user_input.strip())):
                req.response_type = "text"
            # Default to options for steps 1-5
            elif req.step <= 5:
                req.response_type = "options"
            # Step 6 without user input should show prompt
            elif req.step == 6:
                req.response_type = "text"
            else:
                req.response_type = "options"

        # Handle different response types
        if req.response_type == "text":
            # Handle text-based responses (general queries and service-specific queries)
            if not req.user_input or not req.user_input.strip():
                # If this is the initial prompt for general queries, show the prompt
                if req.step == 4 and application_type == "Do you want to ask general queries?":
                    return {
                        "session_id": req.session_id,
                        "intent_id": "001",
                        "intent_name": "general_query_prompt",
                        "response": "Please enter your query:",
                        "response_type": "text",
                        "option_list": "NA",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": req.step
                    }
                # If this is step 6 (service-specific queries), show service query prompt
                elif req.step == 6:
                    return {
                        "session_id": req.session_id,
                        "intent_id": "008",
                        "intent_name": "service_query_prompt",
                        "response": "Please enter your query about the selected service:",
                        "response_type": "text",
                        "option_list": "NA",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": req.step
                    }
                else:
                    return {
                        "session_id": req.session_id,
                        "intent_id": "001",
                        "intent_name": "general_query",
                        "response": "Please enter a query.",
                        "response_type": "text",
                        "option_list": "NA",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": req.step
                    }

            try:                # Get or create collection
                collection_name = get_or_create_collection()

                if not collection_name:
                    return {
                        "session_id": req.session_id,
                        "intent_id": "002",
                        "intent_name": "kb_error",
                        "response": "Sorry, I'm having trouble accessing my knowledge base.",
                        "response_type": "text",
                        "option_list": "NA",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": req.step
                    }

                # Get response from AI chatbot
                # For service-specific queries, add context about the selected service
                query_with_context = req.user_input

                # Check if this is a service-specific query and we have a selected service
                if req.step == 6:
                    selected_service = get_selected_service(req.session_id)
                    if selected_service:
                        query_with_context = f"Question about {selected_service}: {req.user_input}"
                        logger.info(f"Added service context: {selected_service}")

                logger.info(f"Calling ai_chatbot_response with query: '{query_with_context}'")
                result = ai_chatbot_response(
                    query_with_context,
                    collection_name,
                    req.followup_yes,
                    req.followup_no
                )
                logger.info(f"Result from ai_chatbot_response: {result}")

                # --- RAG fallback logic (like /ai_chatbot_ask) ---
                if result["intent_name"] == "fallback":
                    link_data = json_load_rag()
                    rag_res = rag_api(link_data=link_data["silpasathi_links"]["link"], question=req.user_input)
                    result["response"] = f"{rag_res['answer'][0]}<br><br><code>{rag_res['answer'][1]}</code><br><br><b>Disclaimer: This is an AI generated Response. Please verify it from authentic sources.</b>"


                # Return the AI response (possibly updated with RAG)
                return {
                    "session_id": req.session_id,
                    "intent_id": result.get("id", "004"),
                    "intent_name": "ai_response",
                    "response": result["response"],
                    "response_type": "text",
                    "option_list": "NA",
                    "followup_yes": result.get("followup_yes", "NA"),
                    "followup_no": result.get("followup_no", "NA"),
                    "step": req.step
                }
            except Exception as e:
                logger.error(f"Error in text response: {e}")
                return {
                    "session_id": req.session_id,
                    "intent_id": "005",
                    "intent_name": "error",
                    "response": "Sorry, I encountered an error. Please try again.",
                    "response_type": "text",
                    "option_list": "NA",
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": req.step
                }

        elif req.response_type == "options":
            # Step 1: Choose sector/industry
            if req.step == 1:
                button_list = format_options_as_buttons(SECTORS)
                return {
                    "session_id": req.session_id,
                    "intent_id": "101",
                    "intent_name": "choose_sector",
                    "response": "Choose sector or industry:",
                    "response_type": "options",
                    "option_list": button_list,
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": 1
                }

            # Step 2: Choose investment
            elif req.step == 2 and sector:
                button_list = format_options_as_buttons(INVESTMENTS)
                return {
                    "session_id": req.session_id,
                    "intent_id": "102",
                    "intent_name": "choose_investment",
                    "response": "",
                    "response_type": "options",
                    "option_list": button_list,
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": 2
                }

            # Step 3: Application or Query
            elif req.step == 3 and sector and investment:
                store_session(req.session_id, sector, investment)
                options = ["Do you want to apply for licence/clearance?",
                          "Do you want to ask general queries?"]
                button_list = format_options_as_buttons(options)
                return {
                    "session_id": req.session_id,
                    "intent_id": "103",
                    "intent_name": "choose_action",
                    "response": "Choose an option:",
                    "response_type": "options",
                    "option_list": button_list,
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": 3
                }

            # Step 4: Handle "Do you want to ask general queries?" selection
            elif req.step == 4 and application_type == "Do you want to ask general queries?":
                return {
                    "session_id": req.session_id,
                    "intent_id": "104",
                    "intent_name": "general_query_prompt",
                    "response": "Please enter your query:",
                    "response_type": "text",
                    "option_list": "NA",
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": 4
                }

            # Step 4: Pre-establishment or Pre-operational
            elif req.step == 4 and application_type == "Do you want to apply for licence/clearance?":
                options = ["Pre-establishment", "Pre-operational"]
                button_list = format_options_as_buttons(options)
                return {
                    "session_id": req.session_id,
                    "intent_id": "105",
                    "intent_name": "choose_application_type",
                    "response": "Select application type:",
                    "response_type": "options",
                    "option_list": button_list,
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": 4
                }

            # Step 5: Service selection - Pre-establishment
            elif req.step == 5 and service_type == "Pre-establishment":
                button_list = format_options_as_buttons(PRE_ESTABLISHMENT)
                return {
                    "session_id": req.session_id,
                    "intent_id": "106",
                    "intent_name": "choose_pre_establishment",
                    "response": "Select the Pre-establishment service:",
                    "response_type": "options",
                    "option_list": button_list,
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": 5
                }

            # Step 5: Service selection - Pre-operational
            elif req.step == 5 and service_type == "Pre-operational":
                button_list = format_options_as_buttons(PRE_OPERATION)
                return {
                    "session_id": req.session_id,
                    "intent_id": "107",
                    "intent_name": "choose_pre_operation",
                    "response": "Select the Pre-operation service:",
                    "response_type": "options",
                    "option_list": button_list,
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": 5
                }

            # Step 6: After service selection, enable text input for queries
            elif req.step == 6:
                return {
                    "session_id": req.session_id,
                    "intent_id": "108",
                    "intent_name": "service_query_prompt",
                    "response": "Please enter your query about the selected service:",
                    "response_type": "text",
                    "option_list": "NA",
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": 6
                }

            # Invalid step or missing data
            else:
                return {
                    "session_id": req.session_id,
                    "intent_id": "108",
                    "intent_name": "invalid_step",
                    "response": "Invalid step or missing data.",
                    "response_type": "text",
                    "option_list": "NA",
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": req.step
                }

        # Handle other response types if needed
        else:
            return {
                "session_id": req.session_id,
                "intent_id": "999",
                "intent_name": "unsupported_type",
                "response": "Unsupported response type.",
                "response_type": "text",
                "option_list": "NA",
                "followup_yes": "NA",
                "followup_no": "NA",
                "step": req.step
            }

    except Exception as e:
        logger.error(f"Error in chatbot_step: {e}")
        return {
            "session_id": req.session_id if req.session_id else str(uuid.uuid4()),
            "intent_id": "500",
            "intent_name": "error",
            "response": "An error occurred while processing your request.",
            "response_type": "text",
            "option_list": "NA",
            "followup_yes": "NA",
            "followup_no": "NA",
            "step": req.step if req.step else 1
        }

# --- Session Management Endpoint ---
@app.post("/session_manager")
async def session_manager(session_params: SessionIdGenerator):
    timestamp = datetime.datetime.now(tz)
    session_type = session_params.session_type
    session_id = session_params.session_id

    try:
        if session_type == "session_create":
            user_session_id = str(uuid.uuid4())
            # Initialize session in database
            store_session(user_session_id, "", "")

            result = {
                "status": "Session Created!",
                "session_id": user_session_id,
                "time_stamp": timestamp,
            }
        elif session_type == "session_destroy" and session_id:
            # You could implement a function to delete the session
            # For now, we'll just return a success message
            result = {
                "status": "Session Destroyed!",
                "session_id": session_id,
                "time_stamp": timestamp,
            }
        else:
            result = {
                "status": "Invalid session operation",
                "time_stamp": timestamp,
            }

        return result

    except Exception as e:
        logger.error(f"Error processing session request: {str(e)}")
        raise HTTPException(status_code=500, detail="An error occurred while processing the request.")

# # --- AI Chatbot API Endpoints ---
# @app.post("/ai_chatbot_ask")
# async def ai_chatbot_ask(predict_params: PredictParams):

# # def ask_ai_chatbot(req: AIBotRequest):
#     user_input = predict_params.user_input
#     collection_name=predict_params.collection_name
#     followup_yes = predict_params.followup_yes
#     followup_no = predict_params.followup_no
#     """
#     Enhanced chatbot endpoint that uses the AI implementation
#     """
#     connect_to_milvus()

#         # Get response from AI chatbot
#     result = ai_chatbot_response(user_input,collection_name,followup_yes,followup_no)
           

#     if result["intent_name"] == "fallback":
#         link_data = json_load_rag()
#         rag_res = rag_api(link_data=link_data["silpasathi_links"]["link"],question=user_input)
#         # result["response"] = rag_res["answer"][0] + rag_res["answer"][1] + "<br><br><b>Disclaimer: This is an AI generated Response. Please verify it from authentic sources.</b>"
#         #result["rag_res_link"] = rag_res["urls"]
#         result["response"] = f"{rag_res['answer'][0]}<br><br><code>{rag_res['answer'][1]}</code><br><br><b>Disclaimer: This is an AI generated Response. Please verify it from authentic sources.</b>"
#     print('------------------------------------------------')
#     print(result)
#     print('------------------------------------------------')
#     return JSONResponse(content=result)
    

@app.post("/ai_chatbot/create_collection")
def create_ai_collection(req: CreateCollectionRequest):
    """
    Create a new collection for the AI chatbot
    """
    try:
        collection_name = create_collection_and_load_data(req.csv_path)
        if collection_name:
            return {"collection_name": collection_name, "status": "success"}
        else:
            return {"status": "error", "message": "Failed to create collection"}
    except Exception as e:
        logger.error(f"Error creating AI collection: {e}")
        return {"status": "error", "message": str(e)}

@app.get("/ai_chatbot/collections")
def list_collections():
    """
    List all available collections
    """
    try:
        connect_to_milvus()
        collections = get_collections()
        return {"collections": collections}
    except Exception as e:
        logger.error(f"Error listing collections: {e}")
        return {"status": "error", "message": str(e)}

if __name__ == "__main__":
    # Connect to Milvus and initialize collections on startup
    try:
        # Connect to Milvus
        connect_to_milvus()
        logger.info("Connected to Milvus server")

        # Get all available collections directly from Milvus
        collections = utility.list_collections()
        logger.info(f"Available collections at startup: {collections}")

        # Get the latest collection - explicitly sort and take the last one
        if collections:
            sorted_collections = sorted(collections)
            collection_name = sorted_collections[-1]
            logger.info(f"Explicitly selected latest collection at startup: {collection_name}")
        else:
            collection_name = None

        # If no collections exist, create a new one
        if not collection_name:
            logger.info("No collections found. Creating a new collection.")
            csv_path = "eodb_silpasathi_knowledgebase_03_06_25.csv"
            collection_name = create_collection_and_load_data(csv_path)
            if collection_name:
                logger.info(f"Created new collection: {collection_name}")
            else:
                logger.warning("Failed to create collection")
        else:
            logger.info(f"Using latest collection at startup: {collection_name}")
    except Exception as e:
        logger.error(f"Failed to connect to Milvus or initialize collections: {e}")
    
    uvicorn.run(app, host="0.0.0.0", port=8022)
