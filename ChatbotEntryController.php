<?php
class ChatbotEntryController extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->helper(array('form_helper', 'url_helper', 'file_helper'));
        $this->load->library('form_validation', 'auth');
        $this->load->model("super_admin/ChatbotEntryModel");
        $this->admin = $this->session->userdata('admin');
    }

    public function index()
    {
        $data['admin'] = $this->admin;
        //echo"<pre>";print_r($data['admin']);exit;
        $this->load->library(array('auth'));
        if ($this->auth->sa_auth_chck()) {
            $data['collection'] = $this->ChatbotEntryModel->get_department_collection();
            //$data['moduleTypes'] = $this->AiDescriptionEntryModel->fetch_module_type_records();
            if (!isset($_POST['submit'])) {
                //echo "hello";exit;

                //$data['collection']=$this->ChatbotEntryModel->get_department_collection();

                $this->load->view('super_admin/chatbot_entry_view', $data);
            } else {

                $this->form_validation->set_rules('question', 'Question', 'required');
                $this->form_validation->set_rules('answer', 'Answer', 'required');
                $this->form_validation->set_rules('keywordinput', 'Keywords', 'required');
                $this->form_validation->set_rules('department_name', 'Department Name', 'required');


                if ($this->form_validation->run() == FALSE) {
                    $this->load->view('super_admin/chatbot_entry_view', $data);
                } else {
                    $collection_name = $this->input->post('collection_name');
                    $this->db->trans_begin();

                    // Insert document data
                    $data = array(
                        'collection_id_fk' => $this->input->post('department_name'),
                        'question' => $this->input->post('question'),
                        'answer' => $this->input->post('answer'),
                        'keyword' => $this->input->post('keywordinput'),
                        'entry_date'    => date('Y-m-d H:i:sa'),
                        'entry_ip'      => $this->input->ip_address()
                    );

                    //echo"<pre>";print_r($data);exit;


                    $cb_d_id = $this->ChatbotEntryModel->insertChatbotSingleQnA($data); //transaction1

                    if ($cb_d_id) {
                        $this->db->trans_commit();

                        $question_answer_list = $this->ChatbotEntryModel->getQuestionAnswerById($cb_d_id);

                        /*********curl************ */
                        $curl = curl_init();

                        curl_setopt_array($curl, array(
                            CURLOPT_URL => 'http://************:8010/upload_json/',
                            CURLOPT_RETURNTRANSFER => true,
                            CURLOPT_ENCODING => '',
                            CURLOPT_MAXREDIRS => 10,
                            CURLOPT_TIMEOUT => 0,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                            CURLOPT_CUSTOMREQUEST => 'POST',
                            CURLOPT_POSTFIELDS => json_encode([
                                "UploadJSONParams" => [
                                    "collection_name" => $collection_name,
                                    "file" => $question_answer_list
                                ]
                            ]),
                            CURLOPT_HTTPHEADER => array(
                                'Content-Type: application/json'
                            ),
                        ));

                        $response = curl_exec($curl);

                        curl_close($curl);
                        //echo $response;exit;


                        $array_data = json_decode($response, true);
                        $csv_message = $array_data['message'];
                        /************************* */
                        if ($array_data['result'] == "success") {
                            $data = array(
                                'status' => 1,
                            );
                            $question_answer_status = $this->ChatbotEntryModel->updateQuestionAnswerStatusById($cb_d_id, $data);

                            $this->session->set_flashdata('success', 'Data is saved in database and also saved in vector DB.');
                            redirect('chatbot');
                        } else {
                            $data = array(
                                'status' => 0,
                            );
                            $question_answer_status = $this->ChatbotEntryModel->updateQuestionAnswerStatusById($cb_d_id, $data);

                            $this->session->set_flashdata('error', 'Data is saved in database but not saved in vector DB.');
                            redirect('question_answer_list');
                        }
                    } else {
                        $this->db->trans_rollback();
                        $this->session->set_flashdata('error', 'Failed to save data in database. Please try again.');
                        redirect('chatbot');
                    }
                }
            }
        } else {
            redirect(base_url() . 'login');
        }
    }

    public function edit($id = null)
    {
        $data['admin'] = $this->admin;
        $this->load->library(array('auth'));

        // Check if the user is authenticated
        if ($this->auth->sa_auth_chck()) {

            // If ID is provided, fetch the existing record for editing
            if ($id) {
                $data['chatbot_details'] = $this->ChatbotEntryModel->get_chatbot_details_by_id($id);
                $data['keywords'] = $this->ChatbotEntryModel->get_keywords_by_chatbot_id($id);
                //echo"<pre>";print_r($data);exit;
            }

            // If form is submitted
            if ($this->input->post('submit')) {

                // Set validation rules
                $this->form_validation->set_rules('question', 'Question', 'required');
                $this->form_validation->set_rules('answer', 'Answer', 'required');
                // You can uncomment the keyword validation if necessary
                // $this->form_validation->set_rules('keywords', 'Keywords', 'required');

                // If form validation fails
                if ($this->form_validation->run() == FALSE) {
                    $this->load->view('super_admin/chatbot_edit_view', $data);
                } else {
                    /*echo $this->input->post('question');
                    echo $this->input->post('answer');
                    echo"<pre>";print_r($this->input->post('keywords'));exit;*/
                    $this->db->trans_begin(); // Start Transaction

                    // Prepare data for insertion/updation
                    $data = array(
                        'question' => $this->input->post('question'),
                        'answer'   => $this->input->post('answer'),
                    );

                    if ($id) {
                        // Update chatbot entry if ID is present (edit mode)
                        $result = $this->ChatbotEntryModel->update_chatbot_details($id, $data);
                    } else {
                        // Insert new chatbot entry (add mode)
                        $cb_d_id = $result = $this->ChatbotEntryModel->insert_chatbot_details($data);
                    }

                    if ($result || isset($cb_d_id)) {
                        // Handle keyword processing
                        $keywords = $this->input->post('keywords');
                        $keywords_arr = explode(",", $keywords);

                        if (!empty($keywords_arr) && !empty($keywords_arr[0])) {
                            // Prepare data for keywords
                            $keyword_data = array();
                            foreach ($keywords_arr as $keyword) {
                                $keyword_data[] = array(
                                    'id_fk'   => $id ? $id : $cb_d_id, // Use ID if editing, otherwise new entry ID
                                    'keyword' => $keyword,
                                );
                            }

                            if ($id) {
                                // Update keywords for the given chatbot entry
                                $this->ChatbotEntryModel->delete_keywords_by_chatbot_id($id); // Delete existing keywords
                            }

                            $affected_row = $this->ChatbotEntryModel->insert_chatbot_keyword($keyword_data);
                        }

                        if (($affected_row > 0 || $result > 0) && $this->db->trans_status() === TRUE) {
                            // Transaction succeeded
                            $this->db->trans_commit();
                            $this->session->set_flashdata('success', 'Data Successfully Inserted!');
                        } else {
                            // Transaction failed
                            $this->db->trans_rollback();
                            $this->session->set_flashdata('error', 'Failed to save document. Please try again.');
                        }

                        // Redirect to chatbot listing page
                        redirect('chatbot_List');
                    } else {
                        // Rollback if something went wrong
                        $this->db->trans_rollback();
                        $this->session->set_flashdata('error', 'Failed to save document. Please try again.');
                        redirect('chatbot_List');
                    }
                }
            } else {
                //echo "edit view";exit;
                // Load the view for adding or editing an entry
                $this->load->view('super_admin/chatbot_edit_view', $data);
            }
        } else {
            redirect(base_url() . 'login');
        }
    }
    //12-11-24
    public function csvUpload_old()
    {
        $this->load->library('upload');
        $this->load->database(); // Ensure the database is loaded

        // Define configuration for the file upload
        $config['upload_path'] = './readwrite/';
        $config['allowed_types'] = 'csv';  // Allow only CSV files
        $config['max_size'] = 10000;        // Maximum file size (in KB) (Adjust if necessary)

        $this->upload->initialize($config);

        $data['admin'] = $this->admin;

        // Load authentication library
        $this->load->library(array('auth'));
        $this->load->library('csvimport');
        // Check authentication
        if ($this->auth->sa_auth_chck()) {
            // Check if form is submitted
            if (isset($_POST['Submit'])) {
                // Debugging: Check form validation
                //$this->form_validation->set_rules('doc2', 'File', 'required');
                $this->form_validation->set_rules('userfile', 'CSV File', 'callback_file_check_csv');

                if ($this->form_validation->run() == TRUE) {
                    // Perform the file upload
                    /*if (!$this->upload->do_upload('doc2')) {
                        // Upload failed, get the error
                        $error = $this->upload->display_errors();
                        $data['error'] = $error; // Pass error to view
                        echo "Upload Error: " . $error; // Debugging output
                        exit;*/
                    //} else {
                    /********************************** */
                    $collection_name = $this->input->post('collection_name');
                    $department_name = $this->input->post('department_name');
                    $language = $this->input->post('language');
                    $csv_file = $_FILES['doc2']['tmp_name'];
                    $csv_content = file_get_contents($csv_file);
                    $file_data = $this->csvimport->get_array($csv_file);
                    //echo"<pre>";print_r($file_data);exit;
                    foreach ($file_data as $key => $entry) {
                        $file_data[$key]['language'] = $language; // Add language key with value 2
                    }

                    // Print the updated array to verify
                    //echo"<pre>";print_r($file_data);exit;

                    foreach ($file_data as &$row) {
                        $row['collection_id_fk'] = $department_name;
                    }
                    //echo"<pre>";print_r($file_data);exit;
                    // Perform the batch insert
                    //$this->db->insert_batch('chatbot_csv_content', $file_data);
                    $this->db->insert_batch('chatbot_content', $file_data);


                    /*$data_array = array();
                        foreach ($file_data as $key => $row) {
                            $question = isset($row['question']) ? $row['question'] : '';
	                        $answer = isset($row['answer']) ? $row['answer'] : '';
                            $keyword = isset($row['keyword']) ? $row['keyword'] : '';

                            $data_array = array(
		                        'question' => $question,
		                        'answer' => $answer,
		                        'keyword' => $keyword
		                    );
		                    array_push($data_array, $data_array);
                        }*/


                    /*$chatbot_csv_content = array(
                            'csv_content' => $csv_content,
                            'collection_id_fk' => $department_name,
                        );
                        $this->ChatbotEntryModel->insertCsvContent($chatbot_csv_content);*/
                    //echo"<pre>";print_r($csv_content);exit; 
                    $base64_encoded_csv_content = base64_encode($csv_content);
                    //echo"<pre>";print_r($base64_encoded_csv_content);exit; 

                    //11-11-24
                    $question_answer_list = $this->ChatbotEntryModel->getQuestionAnswerList($department_name);
                    //echo"<pre>";print_r($question_answer_list);exit;
                    $question_answer_list_en = json_encode($question_answer_list, JSON_UNESCAPED_UNICODE);
                    //$question_answer_list_en = json_decode(json_encode($question_answer_list), true); // Convert to array
                    //echo"<pre>";print_r($question_answer_list_en);exit;
                    //$question_answer_list_en=json_encode($question_answer_list);
                    //$question_answer_base64=base64_encode($question_answer_list_en);
                    //echo $question_answer_list_en;exit;

                    //echo"<pre>";print_r($question_answer_list);exit;

                    /********csv upload******** */

                    /*$jsonPayload = '{
                            "UploadJSONParams": {
                                "collection_name": "' . $collection_name . '",
                                "file": "' . $question_answer_list_en . '"
                            }
                        }';*/
                    //echo"<pre>";print_r($question_answer_list);exit;
                    //echo $collection_name;exit;
                    /***************************************** */
                    // $curl = curl_init();

                    // curl_setopt_array($curl, array(
                    // //CURLOPT_URL => 'http://10.173.13.28:8064/upload_json/',
                    // CURLOPT_URL => 'http://************:8010/upload_json/',

                    // CURLOPT_RETURNTRANSFER => true,
                    // CURLOPT_ENCODING => '',
                    // CURLOPT_MAXREDIRS => 10,
                    // CURLOPT_TIMEOUT => 0,
                    // CURLOPT_FOLLOWLOCATION => true,
                    // CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    // CURLOPT_CUSTOMREQUEST => 'POST',
                    // /*CURLOPT_POSTFIELDS =>'{
                    // "UploadJSONParams": {
                    //     "collection_name": "'.$collection_name.'",
                    //     "file": "'.$question_answer_list_en.'"
                    // }
                    // }',*/
                    // /*CURLOPT_POSTFIELDS => json_encode([
                    //     "UploadJSONParams" => [
                    //         "collection_name" => $collection_name,
                    //         "file" => $question_answer_list
                    //     ]
                    // ]),*/
                    // CURLOPT_POSTFIELDS =>json_encode([
                    //     "collection_name"=> $collection_name,
                    //     "data"=>$question_answer_list
                    // ]),
                    // CURLOPT_HTTPHEADER => array(
                    //     'Content-Type: application/json'
                    // ),
                    // ));

                    // $response = curl_exec($curl);

                    // curl_close($curl);
                    //echo $response;exit;
                    /******************************************************************** */

                    //echo"<pre>";print_r($question_answer_list);exit;

                    $post_data = [
                        "collection_name" => $collection_name,
                        "data" => $question_answer_list
                    ];

                    // Encode the PHP array to JSON format
                    //$json_data = json_encode($post_data);
                    foreach ($post_data as &$data) {
                        // Check if $data is an array and contains 'id'
                        if (isset($data['id']) && is_string($data['id'])) {
                            $data['id'] = (int) $data['id']; // Ensure 'id' is an integer
                        }
                    }

                    // Convert the data to JSON format
                    $json_data = json_encode($post_data, JSON_NUMERIC_CHECK);

                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/chatbot_upload_json/',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => $json_data,

                        /*CURLOPT_POSTFIELDS =>'{
                        "collection_name": "'.$collection_name.'",
                        "data": [
                            {
                                "id": 62,
                                "intent_name": "new_applicant",
                                "intent_related_text": "what is status of my application? ;  I want to know status of my application ; application status ; my application status ; know my applicant status ; know my application status; applicant status",
                                "response": "Sure. thak you.",
                                "followup_yes": "new_applicantyes11",
                                "followup_no": "new_applicantno11"
                            },
                            {
                                "id": 63,
                                "intent_name": "new_applicant",
                                "intent_related_text": "what is status of my application? ;  I want to know status of my application ; application status ; my application status ; know my applicant status ; know my application status; applicant status",
                                "response": "Sure. thak you.",
                                "followup_yes": "new_applicantyes11",
                                "followup_no": "new_applicantno11"
                            }

                        ]
                        }',*/

                        /*CURLOPT_POSTFIELDS =>'{
                            "collection_name": "'.$collection_name.'",
                            "data": 
                            }',*/
                        /*CURLOPT_POSTFIELDS =>json_encode([
                            "collection_name"=> $collection_name,
                            "data"=>$question_answer_list
                        ]),*/
                        CURLOPT_HTTPHEADER => array(
                            'client_id: 7f156c424b829df15beb9b8803f3a80a',
                            'client_secret: 2a2310f9c49cb8ae679367304b495f95',
                            'Content-Type: application/json'
                        ),
                    ));

                    $response = curl_exec($curl);

                    curl_close($curl);

                    $array_data = json_decode($response, true);

                    /*$post_data = [
                            "collection_name" => $collection_name,
                            "data" => $question_answer_list
                        ];*/

                    // Encode the PHP array to JSON format
                    //$json_data = json_encode($post_data);
                    //echo"<pre>";print_r($json_data);exit;
                    // Convert 'id' to integer
                    /*foreach ($post_data as &$data) {
                            // Check if $data is an array and contains 'id'
                            if (isset($data['id']) && is_string($data['id'])) {
                                $data['id'] = (int) $data['id']; // Ensure 'id' is an integer
                            }
                        }*/

                    // Convert the data to JSON format
                    //$json_data = json_encode($post_data, JSON_NUMERIC_CHECK); // Optional: use JSON_NUMERIC_CHECK for better handling of numeric data

                    // Output the JSON data
                    //echo $json_data;
                    // Output the JSON data
                    //echo $json_data;
                    //exit;
                    /* $curl = curl_init();

                        curl_setopt_array($curl, array(
                        CURLOPT_URL => 'http://************:8010/upload_json/',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS =>$json_data,
                        CURLOPT_HTTPHEADER => array(
                            'Content-Type: application/json'
                        ),
                        ));

                        $response = curl_exec($curl);

                        curl_close($curl);
                        $array_data = json_decode($response, true);
                        echo"res="."<pre>";print_r($array_data);exit;
                        $csv_message=$array_data['message']; */

                    /************************** */



                    /********************************** */
                    // Upload successful
                    /*$upload_data = $this->upload->data(); // Get file data
                        //echo"<pre>";print_r($upload_data);exit;
                        // Read the content of the uploaded CSV file
                        $file_path = $upload_data['full_path'];
                        $file_content = file_get_contents($file_path);

                        // Convert file content to base64 format
                        $base64_encoded_file = base64_encode($file_content);
                        //echo $base64_encoded_file;exit;
                        // Prepare data to be inserted into the database
                        $file_data = array(
                            'file_name' => $upload_data['file_name'],
                            'file_type' => $upload_data['file_ext'],
                            'file_data' => $base64_encoded_file, // Store the base64-encoded content
                        );

                        // Insert the data into the database
                        $this->db->insert('chatbot_csv', $file_data);*/

                    // Set success message
                    if ($array_data['result'] == 'success') {
                        $this->session->set_flashdata('success', 'CSV file uploaded and stored in the database successfully!');
                    } else {
                        $this->session->set_flashdata('error', 'Somthing Went Wrong');
                    }
                    // Redirect back to the same page or another page
                    redirect(base_url() . 'chatbot_csv');
                    //}
                } else {
                    // Validation errors
                    $errors = validation_errors();
                    echo "Validation Error: " . $errors; // Debugging output
                    exit;
                }
            } else {
                $data['collection'] = $this->ChatbotEntryModel->get_department_collection();
                //echo "Form not submitted";
                //exit;
            }

            // Load the view with any error or success messages
            $this->load->view('super_admin/chatbot_csv_upload_view', $data);
        } else {
            // Redirect to login if not authenticated
            redirect(base_url() . 'login');
        }
    }



    public function chatbotList()
    {
        $data['admin'] = $this->admin;
        //echo"<pre>";print_r($data['admin']);exit;
        $this->load->library(array('auth'));
        if ($this->auth->sa_auth_chck()) {
            $data['documents'] = $this->ChatbotEntryModel->get_documents();
            $this->load->view('super_admin/chatbot_list_view', $data);
        } else {
            redirect(base_url() . 'login');
        }
    }

    public function get_csv($id)
    {
        // Fetch the file from the database by ID
        $this->db->where('id', $id);
        $query = $this->db->get('csv_files');
        $file = $query->row();

        if ($file) {
            // Decode the base64 content back into the CSV file
            $csv_data = base64_decode($file->file_data);

            // Optionally, save it as a file
            file_put_contents('./uploads/' . $file->file_name, $csv_data);

            // You can also return or use the decoded CSV data here
            echo "File retrieved and saved!";
        } else {
            echo "File not found!";
        }
    }

    public function chatbotDelete($id)
    {
        if ($id) {
            /********************************************************* */
            /******************************************************** */
            $this->db->trans_begin();
            $result = $this->ChatbotEntryModel->chatbotDeleteQuestion($id);
            echo $result;
            if ($result) {
                $this->session->set_flashdata('success', 'Data Successfully Deleted.');
                $this->db->trans_commit();
                redirect(base_url('chatbot_List'));
            } else {
                $this->db->trans_rollback();
                $this->session->set_flashdata('error', 'This Question May Not Exist. Please Try Again.');
                redirect(base_url('chatbot_List'));
            }
        } else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('error', 'This Question does not Exist. Please Try Again.');
            redirect(base_url('chatbot_List'));
        }
    }





    private function upload_file($file_tmp_name)
    {
        $file_content = file_get_contents($file_tmp_name);
        return base64_encode($file_content);
    }

    public function file_check_csv($str)
    {
        return true;
        // Load the upload library
        $this->load->library('upload');

        // Check if file was uploaded
        if (empty($_FILES['doc2']['name'])) {
            $this->form_validation->set_message('file_check_csv', 'Please select a file to upload.');
            return FALSE;
        }

        // Perform the file upload
        $config['upload_path'] = './readwrite/';
        $config['allowed_types'] = 'csv';
        $config['max_size'] = 2048;

        $this->upload->initialize($config);

        // Attempt to upload the file
        if (!$this->upload->do_upload('doc2')) {
            $this->form_validation->set_message('file_check_csv', $this->upload->display_errors());
            return FALSE;
        } else {

            // File is valid, proceed
            $upload_data = $this->upload->data();
            // Check if the uploaded file is a CSV
            if (strtolower($upload_data['file_ext']) != '.csv') {
                $this->form_validation->set_message('file_check_csv', 'The file must be a CSV.');
                return FALSE;
            }
            return TRUE;
        }
    }
    //12-11-24


    public function departmentList()
    {
        $data['admin'] = $this->admin;
        //echo"<pre>";print_r($data['admin']);exit;
        $this->load->library(array('auth'));
        if ($this->auth->sa_auth_chck()) {
            $data['documents'] = $this->ChatbotEntryModel->get_department();
            $this->load->view('super_admin/chatbot_department_list_view', $data);
        } else {
            redirect(base_url() . 'login');
        }
    }

    public function generateCollection($collection_id)
    {

        $data['admin'] = $this->admin;
        $this->load->library(array('auth'));
        if ($this->auth->sa_auth_chck()) {

            $num_rows = checkDepartment($collection_id);
            //echo $num_rows;exit;
            if ($num_rows == 1) {
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'http://************:8010/generate_collection_name/',
                    //CURLOPT_URL => 'http://10.173.13.28:8063/generate_collection_name/',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                ));

                $response = curl_exec($curl);

                curl_close($curl);
                //echo $response;exit;

                if ($response) {

                    $array_data = json_decode($response, true);
                    $collection_name = $array_data['collection'];
                    $data = array(
                        'collection_name' => $collection_name,
                    );

                    $insert_collection_name = $this->ChatbotEntryModel->generateCollection($collection_id, $data);

                    if ($insert_collection_name) {
                        $this->session->set_flashdata('success', 'Collection name is ' . $collection_name);
                        redirect('chatbot_create_collection');
                    } else {
                        $this->session->set_flashdata('error', 'Collection name is not generated');
                        redirect('chatbot_create_collection');
                    }
                }
            }
        } else {
            redirect(base_url() . 'login');
        }
    }

    public function chatbotQuestionAnswer()
    {
        $question = $this->input->post('question');
        $language = $this->input->post('language');

        //echo $question."Tamal"; die;
        /*$data = array(
            'response' => $question,
            'answer' => "this is the answer",   
        );
        echo json_encode($data);*/

        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
            //echo  $followup_yes;
            /*$followup_yes = "";
            $followup_no = "";*/
        }

        if ($language == "1") { //english
            //echo"<pre>";print_r($_SESSION);
            /*$curl = curl_init();
            //$a=array("question" => $question, "department" => "collection_33a2274c_da71_4239_af26_14cf6c359204", "followup_yes" => $followup_yes, "followup_no" => $followup_no);
            //echo"<pre>";print_r($a);
            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                //CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_1d45baab_4522_420c_a149_c5d1c321fc28", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //rp stakeholder
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_3fc92d7f_1622_4ed9_b55f_f7ef7eeaa9d9", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//rp stakeholder
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_e0922ef8_7c9b_43d9_abc5_6683aaaa94ac", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//rp generic
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);*/

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/chatbot_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30b77844_9e81_4e7a_b023_7b7283d5a333", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //rp stakeholder
                /*CURLOPT_POSTFIELDS => '{
                    "department": "collection_30b77844_9e81_4e7a_b023_7b7283d5a333",
                    "question": "i am 18 year old lady can i get rupashree",
                    "followup_yes": "",
                    "followup_no": ""
                }  ',*/
                CURLOPT_HTTPHEADER => array(
                    'client_id: 7f156c424b829df15beb9b8803f3a80a',
                    'client_secret: 2a2310f9c49cb8ae679367304b495f95',
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            //echo $response;
            $res = json_decode($response, true);
            //echo"<pre>";print_r($res);exit;
            //echo "followup_yes=".$res['followup_yes'];
            //echo "followup_no=".$res['followup_no'];
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        } elseif ($language == "2") { //bengali
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',

                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "Rupashree_ben")),  // Ensure the question is JSON-encoded
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "Rupashree_ben")),  // Ensure the question is JSON-encoded



                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            echo $response;
        }
    }

    public function chatbotFetchSuggestions()
    {
        $question = $this->input->post('question');
        $language = $this->input->post('language');

        //echo $question."Tamal"; die;
        /*$data = array(
            'response' => $question,
            'answer' => "this is the answer",   
        );
        echo json_encode($data);*/

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                CURLOPT_URL => 'http://************:8012/get_suggestions',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',

                CURLOPT_POSTFIELDS => json_encode(array("input_text" => $question, "department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            echo $response;
        } elseif ($language == "2") { //bengali
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                CURLOPT_URL => 'http://************:8011/get_suggestions',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',

                CURLOPT_POSTFIELDS => json_encode(array("input_text" => $question, "department" => "Rupashree_ben")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            echo $response;
        }
    }



    public function fetchSuggestions()
    {
        $question = $this->input->post('question');
        $data = array(
            'response' => $question,
            'answer' => "this is the answer",
        );
        echo json_encode($data);
    }
    /**11-11-24 */




    //12-11-24
    public function chatbotQnaDelete($id, $collection_name)
    {
        /*echo $id . "=" . $collection_name;
        exit;*/
        $intent_id_fk = $this->ChatbotEntryModel->getIntentIdFk($id);

        /*********************************CURL*********************** */
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://************:8015/delete_row/',
            //CURLOPT_URL => 'http://************:8012/delete_row/',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
            "collection_name": "' . $collection_name . '",
            "intent_id": "' . $intent_id_fk . '"
            }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);



        if ($response === false) {
            echo "cURL Error: " . curl_error($curl);
        } else {
            echo $response;
        } //exit;
        curl_close($curl);
        $res = json_decode($response, true);
        $result = $res['result'];
        /*********************************************************** */
        if ($result = "success") {
            $delete_question = $this->ChatbotEntryModel->deleteChatbotQuestion($id);
            if ($delete_question) {
                $delete_intent = $this->ChatbotEntryModel->deleteChatbotIntent($intent_id_fk);
                if ($delete_intent) {
                    //echo $delete_intent;
                    $this->session->set_flashdata('success', 'Data Successfully Deleted from Vector & Local Database.');

                    redirect(base_url('question_answer_list'));
                }
            }
        } elseif ($result == "error" && $res['deleted_count'] == 0) {

            $delete_question = $this->ChatbotEntryModel->deleteChatbotQuestion($id);
            if ($delete_question) {
                $delete_intent = $this->ChatbotEntryModel->deleteChatbotIntent($intent_id_fk);
                if ($delete_intent) {
                    //echo $delete_intent;
                    $this->session->set_flashdata('success', 'Data does not Exist in Vector Database but deleted from local Database');

                    redirect(base_url('question_answer_list'));
                }
            }
        } else {
            $this->session->set_flashdata('error', 'Data is not Deleted.');

            redirect(base_url('question_answer_list'));
        }
    }



    public function eNathikaran()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/ai_e_nathikaran_view', $data);
    }

    /*public function eNathikaranQuestionAnswer(){
        $question = $this->input->post('question');
        $suggestions = array(
            "What should be the language of the document?",
            "What is Document?"
        );
        $answer="this is the anser";
        $data = array(
            'response' => $answer,
            'suggestions' => $suggestions,   
        );
        echo json_encode($data);

    }*/
    public function eNathikaranQuestionAnswer()
    {
        $question = $this->input->post('question');
        //$department="Rupashree";
        //$department="E_nathikaran";

        /*$data = array(
            'response' => $question,
            'answer' => "this is the answer",   
        );
        echo json_encode($data);*/

        $curl = curl_init();

        curl_setopt_array($curl, array(
            //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
            CURLOPT_URL => 'http://************:8010/predict',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',

            CURLOPT_POSTFIELDS => json_encode(array("question" => $question)),  // Ensure the question is JSON-encoded

            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        //print_r($response);exit;
        curl_close($curl);
        echo $response;
    }

    public function chatFeedback()
    {
        $feedback = $this->input->post('feedback');
        $dataid = $this->input->post('dataid');

        //echo "feedback=".$feedback;
        //echo"dataid=".$dataid;
        //CURL

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://************:8012/feedback',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
        "feedback_parameters": {
            "key": "Ailab@123",
            "job_id": "22",
            "intent_id": "' . $dataid . '",
            "feedback_str": "' . $feedback . '"
        }
        }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        echo $response;
    }


    /*public function shilpaSathi()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/shilpa_sathi_view', $data);
    }*/

    // public function shilpaSathiQuestionAnswer(){
    //     $question = $this->input->post('question');
    //     //$department="Rupashree";
    //     //$department="E_nathikaran";

    //     /*$data = array(
    //         'response' => $question,
    //         'answer' => "this is the answer",   
    //     );
    //     echo json_encode($data);*/

    //     $curl = curl_init();

    //     curl_setopt_array($curl, array(
    //     //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
    //     CURLOPT_URL => 'http://************:8010/predict',
    //     CURLOPT_RETURNTRANSFER => true,
    //     CURLOPT_ENCODING => '',
    //     CURLOPT_MAXREDIRS => 10,
    //     CURLOPT_TIMEOUT => 0,
    //     CURLOPT_FOLLOWLOCATION => true,
    //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    //     CURLOPT_CUSTOMREQUEST => 'POST',

    //     CURLOPT_POSTFIELDS => json_encode(array("question" => $question)),  // Ensure the question is JSON-encoded

    //     CURLOPT_HTTPHEADER => array(
    //         'Content-Type: application/json'
    //     ),
    //     ));

    //     $response = curl_exec($curl);
    //     //print_r($response);exit;
    //     curl_close($curl);
    //     echo $response;

    // }

    public function shilpaSathi()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/shilpa_sathi_view', $data);
    }

    public function shilpaSathiQuestionAnswer()
    {
        $question = $this->input->post('question');
        $language = $this->input->post('language');

        //echo $question."Tamal"; die;
        /*$data = array(
            'response' => $question,
            'answer' => "this is the answer",   
        );
        echo json_encode($data);*/

        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_cb79c3db_96fe_4318_8b21_264b9291dcaa", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),

                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "collection_4aa64257_13f0_4913_83cc_80e4910e6893","followup_yes"=>$followup_yes,"followup_no"=>$followup_no)),  
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $res = json_decode($response, true);
            //echo "followup_yes=".$res['followup_yes'];
            //echo "followup_no=".$res['followup_no'];
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        } elseif ($language == "2") { //bengali
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                CURLOPT_URL => 'http://10.173.13.28:8069/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',

                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "Rupashree_ben")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            echo $response;
        }
    }

    public function rpChatbot()
    {
        //echo "hello";exit;
        $this->load->view("ai_rp_chatbot_view");
    }

    public function rpChatbotQuestionAnswer()
    {
        $question = $this->input->post('question');
        $language = $this->input->post('language');
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english

            /************************************ */
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/chatbot_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                "department": "collection_4364aa55_5e2a_4c03_9ec7_b683d06be2e0",
                "question": "' . $question . '",
                "followup_yes": "' . $followup_yes . '",
                "followup_no": "' . $followup_no . '"
              }',
                CURLOPT_HTTPHEADER => array(
                    'client_id: 7f156c424b829df15beb9b8803f3a80a',
                    'client_secret: 2a2310f9c49cb8ae679367304b495f95',
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);

            $res = json_decode($response, true);

            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        } elseif ($language == "2") { //bengali
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/chatbot_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                "department": "collection_4364aa55_5e2a_4c03_9ec7_b683d06be2e0",
                "question": "' . $question . '",
                "followup_yes": "' . $followup_yes . '",
                "followup_no": "' . $followup_no . '"
              }',
                CURLOPT_HTTPHEADER => array(
                    'client_id: 7f156c424b829df15beb9b8803f3a80a',
                    'client_secret: 2a2310f9c49cb8ae679367304b495f95',
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);

            $res = json_decode($response, true);

            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        }
    }


    public function kanyaBot()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/kanya_bot_view', $data);
    }



    public function kanyaBotQuestionAnswer()
    {
        $question = $this->input->post('question');
        $language = $this->input->post('language');

        $arr_institute = array('0701', '0703', '0704');
        $arr_district = array('0304', '0306', '0307', '0308', '0309', '0310', '0311', '0312', '0313');
        $arr_block = array('0501', '0402');
        $login_user = '0701';
        $collection = "";
        if (in_array($login_user, $arr_institute)) { //HOI
            //$collection="collection_189228b3_7309_4a08_bb1d_461dba02949a";
            //$collection="collection_40c23f2c_2f08_4d38_99e0_f3a1666a44ec";
            $collection = "collection_3c27f852_851c_4aba_bdcb_980da343397c";
            //$collection="collection_fda8e853_21b3_42bd_bdcb_bc749a34f37c";

        } elseif (in_array($login_user, $arr_district)) { //DPMU
            $collection = "collection_30a694db_b472_452c_9901_e202e6ff9304";
        } elseif (in_array($login_user, $arr_block)) { //BDO/SDO
            $collection = "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd";
        } else {
            $collection = "";
        }
        //echo $collection;exit;

        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => $collection, "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30a694db_b472_452c_9901_e202e6ff9304", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
                echo "cURL Error: " . curl_error($curl);
            } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        } elseif ($language == "2") { //bengali
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',

                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "Rupashree_ben")),  // Ensure the question is JSON-encoded
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "Rupashree_ben")),  // Ensure the question is JSON-encoded



                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            echo $response;
        }
    }

    //AI Sanlaap

    public function aiSanlaap()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_view', $data);
    }



    public function aiSanlaapBotQuestionAnswer()
    {
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        $language = $this->input->post('language');
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_3c27f852_851c_4aba_bdcb_980da343397c", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30a694db_b472_452c_9901_e202e6ff9304", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
                echo "cURL Error: " . curl_error($curl);
            } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        } elseif ($language == "2") { //bengali
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',

                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "Rupashree_ben")),  // Ensure the question is JSON-encoded
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "Rupashree_ben")),  // Ensure the question is JSON-encoded



                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            echo $response;
        }
    }

    public function chatFormSubmit()
    {
        //echo"From chatFormSubmit";exit;
        //$name = $this->input->post('name');
        $dob = $this->input->post('dob');
        $gender = $this->input->post('gender');
        $marital_status = $this->input->post('marital_status');
        $social_category = $this->input->post('social_category');
        $income = $this->input->post('income');
        $data_res = "dob:" . $dob . ",gender:" . $gender . ",marital_status:" . $marital_status . ",caste:" . $social_category . ",annual_income:" . $income;
        //echo $data_res;exit;

        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://************:8012/predict',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
            "session_id": "' . $session_id . '",
            "collection_id": "",
            "user_response": "' . $data_res . '",
            "response_type": "form",
            "followup_yes": "",
            "followup_no": "",
            "scheme_id": "",
            "department_id": "",
            "criteria_list":[""]
            }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $res = json_decode($response, true);
        $criteria_list = $res['criteria_list'];
        //echo $criteria_list;exit;
        //echo"<pre>";print_r($res['criteria_list']);exit;
        $this->session->set_userdata('criteria_list', $criteria_list);
        $this->session->set_userdata('intent_name', $res['intent_name']);

        //echo"<pre>";print_r($this->session->userdata('criteria_list'));
        curl_close($curl);
        echo $response;
    }




    public function sessionCreate()
    {

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://************:8012/session_id_generator',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            /*CURLOPT_POSTFIELDS => '{
            "session_id_params": {
                "session_type": "session_create",
            }
            }',*/
            CURLOPT_POSTFIELDS => '{
                "session_id_params": {
                  "session_type": "session_create",
                  "session_id": "NA"
                }
            }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        /*echo $response;
        exit;*/
        $res = json_decode($response, true);
        echo "response=" . $response;
        echo "session_id=" . $res['session_id'];
        $this->session->set_userdata('session_id', $res['session_id']);
        //$this->session->set_userdata('followup_no', $res['followup_no']);


    }

    public function sessionDestroy()
    {
        //echo"destroy";exit;
        //echo"<pre>";print_r($_SESSION);exit;
        $session_id = $this->session->userdata('session_id');
        if ($this->session->has_userdata('session_id')) {
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://************:8012/session_id_generator',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                "session_id_params": {
                  "session_type": "session_destroy",
                  "session_id": "' . $session_id . '"
                }
            }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            /*echo $response;
        exit;*/
            $res = json_decode($response, true);

            $this->session->unset_userdata('session_id');

            echo "response=" . $response;


            //echo $_SESSION['session_data']['session_id'];exit;
            //echo"session";exit;

            /*unset($_SESSION['session_data']);

        if (empty($_SESSION['session_data'])) {
            echo "session destroy";
            exit;
        } else {
            echo "session is not destroy";
            exit;
        }*/


            //echo "Session=".$_SESSION['session_data']['session_id'];exit;

        }
    }

    public function aiSanlaapBotQuestionAnswerNew()
    {

        ///////////////////////////////////////////////////////////////////////////////////////////////////////////

        /*echo "session=" . $_SESSION['session_data']['session_date'];

        $session_time = strtotime($_SESSION['session_data']['session_date']);
        $current_time = time();

        $formatted_time = date("h:i:s A", strtotime($session_time));
        echo "formatted_time: " . $formatted_time;
        $current_time1 = date("h:i:s A");
        echo "Current time1: " . $current_time1;

        echo "session_time=".$session_time;
        echo "current_time=".$current_time;exit;
        if (($current_time - $session_time) > 300) { // 300 seconds = 5 minutes
            // Destroy session if more than 5 minutes have passed
            $this->sessionDestroy();

        } else {
            echo "Session is still valid.";
        }*/
        //////////////////////////////////////////////////////////////////////////////////////////////////////////
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        $language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        /*if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }*/
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

        if ($language == "1") { //english

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://************:8012/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                "session_id": "' . $session_id . '",
                "collection_id": "",
                "user_response": "' . $question . '",
                "response_type": "check_eligibility",
                "followup_yes": "",
                "followup_no": "",
                "scheme_id": "",
                "department_id": "",
                "criteria_list":[""]
                }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $res = json_decode($response, true);
            echo $response;
        }
    }

    public function schemeBucket()
    {


        $question = $this->input->post('scheme_bucket');
        $intent_name = $this->session->userdata('intent_name');
        //echo "intent_name=".$intent_name;
        //echo "question=".$question;
        $res_data = $intent_name . ":" . $question;
        //echo "res_data=".$res_data;
        //echo "criteria_list=".json_encode($this->session->userdata('criteria_list'));
        //$res_data = "scheme_bucket:".$question;
        //echo "scheme_bucket=".$res_data;
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
            $criteria_list = $this->session->userdata('criteria_list');
            $criteria_list_json = json_encode($criteria_list);
            //echo "session_id=".$session_id;
            //echo"criteria_list="."<pre>";print_r($criteria_list);exit;

            /*}

        if ($session_id) { */

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://************:8012/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                    "session_id": "' . $session_id . '",
                    "collection_id": "",
                    "user_response": "' . $res_data . '",
                    "response_type": "options",
                    "followup_yes": "",
                    "followup_no": "",
                    "scheme_id": "",
                    "department_id": "",
                    "criteria_list": ' . $criteria_list_json . '
                  }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $res = json_decode($response, true);
            //echo"<pre>";print_r($res);
            $this->session->set_userdata('intent_name', $res['intent_name']);
            $this->session->set_userdata('criteria_list', $res['criteria_list']);

            echo $response;
            //exit;
        } else {
            echo "Empty Session";
        }
    }

    //////////////////////////rag kp////////////////////////////////////

    //AI Sanlaap

    public function aiSanlaapRagKp()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_rag_kp_view', $data);
    }



    public function aiSanlaapBotRagKpQuestionAnswer()
    {
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        //$language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                // CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_URL => 'http://************:8010/kp_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_5d0d1655_2daa_4deb_b044_b5aa3f1fed1c", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30a694db_b472_452c_9901_e202e6ff9304", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
             echo "cURL Error: " . curl_error($curl);
         } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        }
    }

    // ////////////////////////rag rp/////////////////////

    public function aiSanlaapRagRp()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_rag_rp_view', $data);
    }


    public function aiSanlaapBotRagRpQuestionAnswer()
    {
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        //$language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                // CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_URL => 'http://************:8010/rp_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_1d45baab_4522_420c_a149_c5d1c321fc28", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30a694db_b472_452c_9901_e202e6ff9304", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
             echo "cURL Error: " . curl_error($curl);
         } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        }
    }

    // ////////////////////////Laxmir bhandar rp/////////////////////

    public function aiSanlaapRagLb()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_rag_lb_view', $data);
    }


    public function aiSanlaapBotRagLbQuestionAnswer()
    {
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        //$language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                // CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_URL => 'http://************:8010/lb_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_5d0d1655_2daa_4deb_b044_b5aa3f1fed1c", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30a694db_b472_452c_9901_e202e6ff9304", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
              echo "cURL Error: " . curl_error($curl);
          } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        }
    }

    // ////////////////////////Wps Rag/////////////////////

    public function aiSanlaapRagWps()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_rag_wps_view', $data);
    }


    public function aiSanlaapBotRagWpsQuestionAnswer()
    {
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        //$language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                // CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_URL => 'http://************:8010/wps_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_5d0d1655_2daa_4deb_b044_b5aa3f1fed1c", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30a694db_b472_452c_9901_e202e6ff9304", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
              echo "cURL Error: " . curl_error($curl);
          } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        }
    }


    // ////////////////////////Wps Rag/////////////////////

    public function aiSanlaapRagOps()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_rag_ops_view', $data);
    }


    public function aiSanlaapBotRagOpsQuestionAnswer()
    {
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        //$language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                // CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_URL => 'http://************:8010/ops_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_5d0d1655_2daa_4deb_b044_b5aa3f1fed1c", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_5d0d1655_2daa_4deb_b044_b5aa3f1fed1c", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
              echo "cURL Error: " . curl_error($curl);
          } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        }
    }

    // ////////////////////////JB Rag/////////////////////

    public function aiSanlaapRagJb()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_rag_jb_view', $data);
    }


    public function aiSanlaapBotRagJbQuestionAnswer()
    {
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        //$language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                // CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_URL => 'http://************:8010/jb_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_5d0d1655_2daa_4deb_b044_b5aa3f1fed1c", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30a694db_b472_452c_9901_e202e6ff9304", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
              echo "cURL Error: " . curl_error($curl);
          } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        }
    }

    public function aiSocialRegistry()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_social_registry_view', $data);
    }



    public function aiSocialRegistryQuestionAnswer()
    {
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        //$language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                // CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_URL => 'http://************:8012/kp_predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_5d0d1655_2daa_4deb_b044_b5aa3f1fed1c", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30a694db_b472_452c_9901_e202e6ff9304", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
             echo "cURL Error: " . curl_error($curl);
         } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        }
    }

    public function webRagDataSource()
    {
        $data['admin'] = $this->admin;
        //echo"<pre>";print_r($data['admin']);exit;
        $this->load->library(array('auth'));
        if ($this->auth->sa_auth_chck() || $this->auth->nic_developer_auth_chck()) {
            if (!isset($_POST['submit'])) {
                //echo "hello";exit;
                $this->load->view('super_admin/ai_web_rag_data_source_view', $data);
            } else {


                $this->form_validation->set_rules('question_count', 'question_count', 'required');
                //$this->form_validation->set_rules('link1', 'Link1', 'required');



                if ($this->form_validation->run() == FALSE) {
                    echo "validation faild";
                    exit;
                    //echo "ht123";exit;
                    $this->load->view('super_admin/ai_web_rag_data_source_view', $data);
                } else {
                    echo "validation success";
                    exit;
                    $links = [];
                    foreach ($_POST as $key => $value) {
                        if (strpos($key, 'link') === 0 && !empty($value)) {
                            $links[] = $value;  // Collect only the non-empty link inputs
                        }
                    }

                    /*echo"<pre>";print_r($links);
					$imp=implode(",",$links);
					echo $imp;*/
                    $question = $this->input->post('question');
                    $json = json_encode($links, JSON_UNESCAPED_SLASHES);
                    /*echo "question=".$question;
					echo $json;exit;*/

                    /********************************************************* */

                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/web_rag',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => '{
								"params" :{
									"key": "Ailab@123",
									"job_id": "5",
									"url_lst": ' . $json . ',
									"qus_str": "' . $question . '"
									}
									
							}',
                        CURLOPT_HTTPHEADER => array(
                            'client_id: 7d84dff9e324f19a65ebf32c9fe0030a',
                            'client_secret: 57ec1c37c60454b31f5494913a048d81',
                            'Content-Type: application/json'
                        ),
                    ));

                    $response = curl_exec($curl);
                    //echo"<pre>";print_r(json_decode($response,true));exit;
                    $res = json_decode($response, true);
                    //$data['result']=$res['result'];
                    //$data['status']=$res['status'];
                    //$data['urls']=$res['urls'];
                    $data['res'] = $res;
                    curl_close($curl);
                    //echo $response;
                    /******************************************************** */
                    $this->load->view('super_admin/ai_web_rag_data_source_view', $data);
                }
            }
        } else {
            redirect(base_url() . 'login');
        }
    }
    public function webRagDataSourceInsert()
    {

        $datasource_type = $this->input->post('datasource_type');
        $question_count = $this->input->post('question_count');

        //echo "datasource_type = $datasource_type<br>";
        //echo "question_count = $question_count<br>";

        if ($datasource_type === 'pdf' && isset($_FILES['pdf'])) {
            $file = $_FILES['pdf'];
            //echo "PDF File Name = " . $file['name'] . "<br>";
            //echo "PDF File Size = " . $file['size'] . "<br>";
            $file = $_FILES['pdf']['tmp_name'];
            $file_content = file_get_contents($file);
            $base64_string = base64_encode($file_content);
            $base64_string_cbc = cbcEncryption($base64_string);
            //$j_link1 = $base64_string_cbc;
            $j_link = json_encode($base64_string_cbc);
            $question_count = json_encode($question_count);
            $this->session->set_userdata('pdf_string', $j_link);
            /*echo "j_link1=".$j_link1;
            echo "j_link2=".$j_link2;exit;*/

            //echo "base64_string=".$base64_string."<br>";
            // Optionally move it
            // move_uploaded_file($file['tmp_name'], './uploads/' . $file['name']);
            $data = array(
                'source_type' => $datasource_type,
                'file' => $base64_string,
                'question_count' => $question_count
            );
        } elseif ($datasource_type === 'link') {
            $link = $this->input->post('link');
            //echo "Link = $link<br>";
            $data = array(
                'source_type' => $datasource_type,
                'url' => $link,
                'question_count' => $question_count
            );

            $j_link = json_encode($link, JSON_UNESCAPED_SLASHES);
            $question_count = json_encode($question_count);
        }
        $this->db->trans_begin();
        //$data_source_id = $this->ChatbotEntryModel->insertDataSourceData($data); //transaction1

        //if ($data_source_id) {
        //$j_link = json_encode($link, JSON_UNESCAPED_SLASHES);
        //$question_count = json_encode($question_count);
        //$this->db->trans_commit();

        /*$response = [
                'status' => 'success',
                'message' => 'question list fetch',
                'job_id' => '6',
                'result' => [
                    "What is an example of a solid state of water in nature?",
                    "Which process allows water to turn from liquid to gas?",
                    "What is the process that turns water into ice?"
                ]
            ];
            //echo $data_source_id;
            $result=json_encode($response);
            $res=json_decode($result,true);
            $res['insert_id']=$data_source_id;

            echo json_encode($res);*/

        //echo "j_link= ".$j_link;exit;

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/web_rag',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                    "params" :{
                        "key": "Ailab@123",
                        "job_id": "5",
                        "task_id": "2",
                        "input_lst": [' . $j_link . '],
                        "qus_str": "",
                        "no_of_ques":' . $question_count . '
                        }
                        
                }',
            CURLOPT_HTTPHEADER => array(
                'client_id: 7d84dff9e324f19a65ebf32c9fe0030a',
                'client_secret: 57ec1c37c60454b31f5494913a048d81',
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        //echo"<pre>";print_r($response);exit;
        //echo $response;
        $res = json_decode($response, true);
        /*echo "<pre>";
            print_r($res);
            exit;*/
        if (!empty($res)) {
            $data_source_id = $this->ChatbotEntryModel->insertDataSourceData($data); //transaction1
            if ($data_source_id) {
                $this->db->trans_commit();
            } else {
                $this->db->trans_rollback();
            }
            $res['insert_id'] = $data_source_id;
            echo json_encode($res);
        } else {
            echo "Response Error";
        }



        /**************************************************** */
        /*$que=$res['question'];
            foreach ($que as $question) {

                $data = array(
                    'question' => $question,
                    'source_id' => $data_source_id

                );
                $this->db->trans_begin();
                $data_source_id = $this->ChatbotEntryModel->insertDataSourceQuestion($data); //transaction1
                if ($data_source_id) {
                    $this->db->trans_commit();
                }
            }*/
        /**************************************************** */


        //$this->db->trans_commit();
        // } else {
        //$this->db->trans_rollback();
        //}
    }

    public function fetchDataFromQuestion()
    {

        //echo"hello";exit;
        $question = $this->input->post('question');
        $data_id = $this->input->post('data_id');
        $data_link = $this->input->post('data_link');
        $data_count = $this->input->post('data_count');
        $data_count = '5';
        $data_pdf = $this->input->post('data_pdf');
        /*echo "data_link=".$data_link;
        echo "data_pdf=".$data_pdf;exit;*/
        if ($data_link != "") {
            $j_link = json_encode($data_link, JSON_UNESCAPED_SLASHES);
        } else {
            //$j_link = json_encode($data_pdf);

            /*$file = $_FILES['pdf']['tmp_name'];
            $file_content = file_get_contents($file);
            $base64_string = base64_encode($file_content);
            $base64_string_cbc=cbcEncryption($base64_string);
            echo"base64_string_cbc=".$base64_string_cbc;exit;*/
            $j_link = $this->session->userdata('pdf_string');
            //echo "j_link=".$j_link;exit;


        }
        //echo "j_link=".$j_link;

        //$j_link = json_encode($data_link, JSON_UNESCAPED_SLASHES);
        $question = json_encode($question);
        $data_count = json_encode($data_count);

        /*$response = [
            'status' => 'success',
            'message' => 'question list fetch',
            'job_id' => '6',
            'result' => [
                "What is an example of a solid state of water in nature?",
                "Which process allows water to turn from liquid to gas?",
                "What is the process that turns water into ice?"
            ]
        ];

        echo json_encode($response);*/
        /******************task-id1 to get answer********************************** */

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/web_rag',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "params" :{
                    "key": "Ailab@123",
                    "job_id": "5",
                    "task_id": "1",
                    "input_lst": [' . $j_link . '],
                    "qus_str": ' . $question . ',
                    "no_of_ques":""
                    }
                    
            }',
            CURLOPT_HTTPHEADER => array(
                'client_id: 7d84dff9e324f19a65ebf32c9fe0030a',
                'client_secret: 57ec1c37c60454b31f5494913a048d81',
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        //echo $response;exit;
        $res = json_decode($response, true);
        $ans = $res['answer'];
        //echo"<pre>";print_r($ans);exit;
        /************************************************************************* */

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/web_rag',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "params" :{
                    "key": "Ailab@123",
                    "job_id": "5",
                    "task_id": "3",
                    "input_lst": [' . $j_link . '],
                    "qus_str": ' . $question . ',
                    "no_of_ques":' . $data_count . '
                    }
                    
            }',
            CURLOPT_HTTPHEADER => array(
                'client_id: 7d84dff9e324f19a65ebf32c9fe0030a',
                'client_secret: 57ec1c37c60454b31f5494913a048d81',
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        //echo $response;exit;

        $res = json_decode($response, true);

        /*****************************store questions in table*******************  */
        $updatedQuestions = $res['question'];
        foreach ($updatedQuestions as $question) {

            $data = array(
                'question' => $question,
                'source_id' => $data_id

            );
            $this->db->trans_begin();
            $data_source_id = $this->ChatbotEntryModel->insertDataSourceQuestion($data); //transaction1
            if ($data_source_id) {
                $this->db->trans_commit();
            } else {
                $this->db->trans_rollback();
            }
        }
        /************************************************************************** */
        $res['answer'] = $ans;
        $res2 = json_encode($res);
        echo $res2;
    }

    public function ragQuestionAnswerSet()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Collect form data
            $id = $_POST['id'];
            $intent = $_POST['intent'];
            $answer = $_POST['answer'];
            $updatedQuestions = $_POST['updatedQuestions']; // Assuming this is an array
            $imp = $question = implode(";", $updatedQuestions);

            /************************************************************** */
            $department_id = 22;
            $collection_name = getCollection($department_id);
            //echo "collection_name=".$collection_name;exit;
            //$language = $this->input->post('language');
            $this->db->trans_begin();

            //Insert data in chatbot_intent table 
            $intent_data = array(
                'intent_name' => $intent,
                'collection_id_fk' => $department_id,
                'followup_yes' => ($this->input->post('follow_yes') != "") ? $this->input->post('follow_yes') : '',
                'followup_no' => $this->input->post('follow_no'),
                'status' => 1
            );

            $intent_id = $this->ChatbotEntryModel->insertIntent($intent_data);
            if ($intent_id) {

                $question_data = array(
                    'intent_id_fk' => $intent_id,
                    'question' => $question,
                    'answer' => $answer,
                    'status' => 1

                );

                $question_id = $this->ChatbotEntryModel->insertQuestion($question_data);
                if ($question_id) {
                    $this->db->trans_commit();
                    //to get data 
                    $send_data = $this->ChatbotEntryModel->getQuestionAnswerId($intent_id);
                    //echo"<pre>";print_r($send_data);exit;
                    $question_array = explode(";", $send_data['question']);
                    $question_array = array_map('trim', $question_array);
                    $send_data['question'] = $question_array;
                    //echo"<pre>";print_r($send_data);
                    //Making insert data
                    $final_data = array();
                    for ($i = 0; $i < count($question_array); $i++) {
                        $final_data[$i]['intent_name'] = $send_data['intent_name'];
                        $final_data[$i]['intent_related_text'] = $send_data['question'][$i];
                        $final_data[$i]['response'] = $send_data['answer'];
                        $final_data[$i]['followup_yes'] = "";
                        $final_data[$i]['followup_no'] = "";
                        //$final_data[$i]['followup_yes'] = ($send_data['followup_yes'])? $send_data['followup_yes']:"" ;
                        //$final_data[$i]['followup_no'] = ($send_data['followup_no'])? $send_data['followup_no']:"" ;
                        $final_data[$i]['intent_id'] = $send_data['intent_id'];
                    }
                    //echo"<pre>";print_r($final_data);exit;
                    //Have to send this array to milvas
                    $jdata = json_encode($final_data);
                    /************************************curl**************** */
                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'http://************:8010/upload_json/',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => '{
                                "collection_name": "' . $collection_name . '",
                                "data": ' . $jdata . '
                                }',
                        CURLOPT_HTTPHEADER => array(
                            'Content-Type: application/json'
                        ),
                    ));

                    $response = curl_exec($curl);

                    curl_close($curl);
                    //echo $response;exit;
                    $curl_res = json_decode($response, true);
                    $curl_result = $curl_res['result'];
                    // /************************************************************** */

                    if ($curl_result == "success") {

                        $intent_data = array(
                            'vdb_status' => 1

                        );

                        $intentResult = $this->ChatbotEntryModel->updateChatbotIntent($intent_id, $intent_data);
                        //$this->session->set_flashdata('success', 'Data is Saved in Local & Vector Database.');
                        //redirect('chatbot_singla_qna');
                        //echo"Data is Saved in Local & Vector Database.";
                        echo $response;
                    } else {
                        //echo"Data is Saved in Local Database but Not Saved in Vector Database";
                        //$this->session->set_flashdata('error', 'Data is Saved in Local Database but Not Saved in Vector Database');
                        //redirect('chatbot_singla_qna');
                        echo $response;
                    }
                    //$this->db->trans_commit();
                    //$this->session->set_flashdata('success', 'Data is saved in database.');
                    //redirect('chatbot_singla_qna');
                } else {
                    $this->db->trans_rollback();
                    //echo $response;
                    echo "Failed to save data in database. Please try again.";
                    //$this->session->set_flashdata('error', 'Failed to save data in database. Please try again.');
                    //redirect('chatbot_singla_qna');
                }
            }

            /************************************************************** */

            $this->db->trans_begin();

            //Insert data in chatbot_intent table 
            $intent_data = array(
                'intent_name' => $intent,
                'collection_id_fk' => "10",
                'followup_yes' => ($this->input->post('follow_yes') != "") ? $this->input->post('follow_yes') : '',
                'followup_no' => $this->input->post('follow_no'),
                'status' => 1
            );

            $intent_id = $this->ChatbotEntryModel->insertIntent($intent_data);
            if ($intent_id) {

                $question_data = array(
                    'intent_id_fk' => $intent_id,
                    'question' => $imp,
                    'answer' => $answer,
                    'status' => 1

                );

                $question_id = $this->ChatbotEntryModel->insertQuestion($question_data);
            }
        }
    }

    public function ragQuestionAnswerSet_old()
    {


        //echo "from ragQuestionAnswerSet";exit;
        /*echo $this->input->post('id');
        echo $this->input->post('intent');
        echo $this->input->post('answer');
        echo"<pre>";print_r($this->input->post('updatedQuestions'));
        exit;*/

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Collect form data
            $id = $_POST['id'];
            $intent = $_POST['intent'];
            $answer = $_POST['answer'];
            $updatedQuestions = $_POST['updatedQuestions']; // Assuming this is an array
            $imp = implode(";", $updatedQuestions);

            /************************************************************ */
            /*$curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://************:8014/upload_json/',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                "collection_name": "collection_f8280995_d54a_4fce_916e_df56a026a0f3",
                "data": [
                    {
                        "id": 200,
                        "intent_name": "' . $intent . '",
                        "intent_related_text": "' . $imp . '",
                        "response": "' . $answer . '",
                        "followup_yes": "",
                        "followup_no": ""
                    }
                    
                ]
                
                }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            $res = json_decode($response, true);
            curl_close($curl);
            echo"<pre>";print_r($res);exit;*/
            /************************************************************ */
            /*echo"answer".$answer;
            echo"intent=".$intent;
            echo"<pre>";print_r($updatedQuestions);exit;*/
            // Process the data (e.g., update the database)

            /*foreach ($updatedQuestions as $question) {

                $data = array(
                    'question' => $question,
                    'source_id' => $id

                );
                $this->db->trans_begin();
                $data_source_id = $this->ChatbotEntryModel->insertDataSourceQuestion($data); //transaction1
                if ($data_source_id) {
                    $this->db->trans_commit();
                }
            }*/

            /************************************************************* */

            /********************************16-5-25**************************** */
            $this->db->trans_begin();

            //Insert data in chatbot_intent table 
            $intent_data = array(
                'intent_name' => $intent,
                'collection_id_fk' => "10",
                'followup_yes' => ($this->input->post('follow_yes') != "") ? $this->input->post('follow_yes') : '',
                'followup_no' => $this->input->post('follow_no'),
                'status' => 1
            );

            $intent_id = $this->ChatbotEntryModel->insertIntent($intent_data);
            if ($intent_id) {

                $question_data = array(
                    'intent_id_fk' => $intent_id,
                    'question' => $imp,
                    'answer' => $answer,
                    'status' => 1

                );

                $question_id = $this->ChatbotEntryModel->insertQuestion($question_data);
            }
            /****************************************************************** */

            //$idd=200.''.$id;
            // $this->db->trans_begin();

            // // Insert document data
            // $data = array(
            //     'collection_id_fk' => "10",
            //     'intent_name' => $intent,
            //     'intent_related_text'   => $imp,
            //     'response'   => $answer

            // );

            // //echo"<pre>";print_r($data);exit;


            // $cb_d_id = $this->ChatbotEntryModel->insertDataWebrag($data); //transaction1

            if ($question_id) {
                $this->db->trans_commit();
                // $curl = curl_init();

                // curl_setopt_array($curl, array(
                //     CURLOPT_URL => 'http://************:8014/upload_json/',
                //     CURLOPT_RETURNTRANSFER => true,
                //     CURLOPT_ENCODING => '',
                //     CURLOPT_MAXREDIRS => 10,
                //     CURLOPT_TIMEOUT => 0,
                //     CURLOPT_FOLLOWLOCATION => true,
                //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                //     CURLOPT_CUSTOMREQUEST => 'POST',
                //     CURLOPT_POSTFIELDS => '{
                // "collection_name": "collection_f8280995_d54a_4fce_916e_df56a026a0f3",
                // "data": [
                //     {
                //         "id": ' . $question_id . ',
                //         "intent_name": "' . $intent . '",
                //         "intent_related_text": "' . $imp . '",
                //         "response": "' . $answer . '",
                //         "followup_yes": "",
                //         "followup_no": ""
                //     }

                // ]

                // }',
                //     CURLOPT_HTTPHEADER => array(
                //         'Content-Type: application/json'
                //     ),
                // ));

                // $response = curl_exec($curl);
                // $res = json_decode($response, true);
                // curl_close($curl);
                // if (!empty($res)) {
                //     $this->db->trans_commit();
                //     echo $response;
                // } else {
                //     $this->db->trans_rollback();
                //     echo "Response faild";
                // }
            }
            //exit;
            /************************************************************* */

            // Example response
            /*$response = array(
                'status' => 'success',
                'message' => 'Data processed successfully',
                'updatedQuestions' => $updatedQuestions
            );

            // Send a JSON response back to the client
            echo json_encode($response);*/
        }
    }
    /*****************************16-5-25******************************* */
    public function dataSourceQuestionAnswerList()
    {
        $data['admin'] = $this->admin;
        //echo"<pre>";print_r($data['admin']);exit;
        $this->load->library(array('auth'));
        if ($this->auth->sa_auth_chck()) {
            if (!isset($_POST['submit'])) {
                $data['department'] = $this->ChatbotEntryModel->get_department();
                $data['collection'] = $this->ChatbotEntryModel->get_department_collection();

                //$data['qna']=$this->ChatbotEntryModel->getQuestionAnswerList();
                $this->load->view('super_admin/data_source_question_answer_list_view', $data);
            } else {
                /*$department_id=$this->input->post('department_id');
                echo json_encode($department_id);*/
            }
        } else {
            redirect(base_url() . 'login');
        }
    }

    public function getDataSourceQuestionAnswerAjax()
    {
        $department_id = $this->input->post('department_id');
        //echo json_encode($department_id);
        $qna = $this->ChatbotEntryModel->getQuestionAnswerList($department_id);
        //echo"<pre>";print_r($qna);exit;
        echo json_encode($qna);
    }

    /***************************************15-5-25************************** */
    public function chatbotSinglaQnA()
    {

        $data['admin'] = $this->admin;
        //echo"<pre>";print_r($data['admin']);exit;
        $this->load->library(array('auth'));
        if ($this->auth->sa_auth_chck() || $this->auth->nic_developer_auth_chck()) {
            $data['collection'] = $this->ChatbotEntryModel->get_department_collection();
            //$data['moduleTypes'] = $this->AiDescriptionEntryModel->fetch_module_type_records();
            if (!isset($_POST['submit'])) {
                //echo "hello";exit;

                //$data['collection']=$this->ChatbotEntryModel->get_department_collection();

                $this->load->view('super_admin/chatbot_entry_view', $data);
            } else {
                /*echo '<pre>';
                print_r($this->input->post());
                exit;*/
                $this->form_validation->set_rules('department_name', 'Department Name', 'required');
                $this->form_validation->set_rules('intent', 'Intent', 'required');
                $this->form_validation->set_rules('question', 'Question', 'required');
                $this->form_validation->set_rules('answer', 'Answer', 'required');




                if ($this->form_validation->run() == FALSE) {
                    $this->load->view('super_admin/chatbot_entry_view', $data);
                } else {
                    //echo"hhh";exit;
                    //$collection_name=$this->input->post('collection_name');
                    $collection_name = getCollection($this->input->post('department_name'));
                    //echo "collection_name=".$collection_name;exit;
                    //$language = $this->input->post('language');
                    $this->db->trans_begin();

                    //Insert data in chatbot_intent table 
                    $intent_data = array(
                        'intent_name' => $this->input->post('intent'),
                        'collection_id_fk' => $this->input->post('department_name'),
                        'followup_yes' => ($this->input->post('follow_yes') != "") ? $this->input->post('follow_yes') : '',
                        'followup_no' => $this->input->post('follow_no'),
                        'status' => 1
                    );

                    $intent_id = $this->ChatbotEntryModel->insertIntent($intent_data);
                    if ($intent_id) {

                        $question_data = array(
                            'intent_id_fk' => $intent_id,
                            'question' => $this->input->post('question'),
                            'answer' => $this->input->post('answer'),
                            'status' => 1,
                            'entry_date'    => date('Y-m-d H:i:sa'),
                            'entry_ip'      => $this->input->ip_address()

                        );

                        $question_id = $this->ChatbotEntryModel->insertQuestion($question_data);
                        if ($question_id) {
                            $this->db->trans_commit();
                            //to get data 
                            $send_data = $this->ChatbotEntryModel->getQuestionAnswerId($intent_id);
                            //echo"<pre>";print_r($send_data);exit;
                            $question_array = explode(";", $send_data['question']);
                            $question_array = array_map('trim', $question_array);
                            $send_data['question'] = $question_array;
                            //echo"<pre>";print_r($send_data);
                            //Making insert data
                            $final_data = array();
                            for ($i = 0; $i < count($question_array); $i++) {
                                $final_data[$i]['intent_name'] = $send_data['intent_name'];
                                $final_data[$i]['intent_related_text'] = $send_data['question'][$i];
                                $final_data[$i]['response'] = $send_data['answer'];
                                $final_data[$i]['followup_yes'] = $send_data['followup_yes'];
                                $final_data[$i]['followup_no'] = $send_data['followup_no'];
                                $final_data[$i]['intent_id'] = $send_data['intent_id'];
                            }
                            //echo"<pre>";print_r($final_data);exit;
                            //Have to send this array to milvas
                            $jdata = json_encode($final_data);
                            /************************************curl**************** */
                            $curl = curl_init();

                            curl_setopt_array($curl, array(
                                CURLOPT_URL => 'http://************:8010/upload_json/',
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_ENCODING => '',
                                CURLOPT_MAXREDIRS => 10,
                                CURLOPT_TIMEOUT => 0,
                                CURLOPT_FOLLOWLOCATION => true,
                                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                CURLOPT_CUSTOMREQUEST => 'POST',
                                CURLOPT_POSTFIELDS => '{
                                "collection_name": "' . $collection_name . '",
                                "data": ' . $jdata . '
                                }',
                                CURLOPT_HTTPHEADER => array(
                                    'Content-Type: application/json'
                                ),
                            ));

                            $response = curl_exec($curl);

                            curl_close($curl);
                            //echo $response;exit;
                            $curl_res = json_decode($response, true);
                            $curl_result = $curl_res['result'];
                            // /************************************************************** */

                            if ($curl_result == "success") {

                                $intent_data = array(
                                    'vdb_status' => 1

                                );

                                $intentResult = $this->ChatbotEntryModel->updateChatbotIntent($intent_id, $intent_data);
                                $this->session->set_flashdata('success', 'Data is Saved in Local & Vector Database.');
                                redirect('chatbot_singla_qna');
                            } else {
                                $this->session->set_flashdata('error', 'Data is Saved in Local Database but Not Saved in Vector Database');
                                redirect('chatbot_singla_qna');
                            }
                            //$this->db->trans_commit();
                            //$this->session->set_flashdata('success', 'Data is saved in database.');
                            redirect('chatbot_singla_qna');
                        } else {
                            $this->db->trans_rollback();
                            $this->session->set_flashdata('error', 'Failed to save data in database. Please try again.');
                            redirect('chatbot_singla_qna');
                        }
                    }
                }
            }
        } else {
            redirect(base_url() . 'login');
        }
    }

    public function csvUpload()
    {
        $this->load->library('upload');
        $this->load->database(); // Ensure the database is loaded

        // Define configuration for the file upload
        $config['upload_path'] = './readwrite/';
        $config['allowed_types'] = 'csv';  // Allow only CSV files
        $config['max_size'] = 10000;        // Maximum file size (in KB) (Adjust if necessary)

        $this->upload->initialize($config);

        $data['admin'] = $this->admin;

        // Load authentication library
        $this->load->library(array('auth'));
        $this->load->library('csvimport');
        // Check authentication
        if ($this->auth->sa_auth_chck() || $this->auth->nic_developer_auth_chck()) {
            // Check if form is submitted
            if (isset($_POST['Submit'])) {
                // Debugging: Check form validation
                //$this->form_validation->set_rules('doc2', 'File', 'required');
                $this->form_validation->set_rules('userfile', 'CSV File', 'callback_file_check_csv');

                if ($this->form_validation->run() == TRUE) {
                    // Perform the file upload
                    /*if (!$this->upload->do_upload('doc2')) {
                        // Upload failed, get the error
                        $error = $this->upload->display_errors();
                        $data['error'] = $error; // Pass error to view
                        echo "Upload Error: " . $error; // Debugging output
                        exit;*/
                    //} else {
                    /********************************** */
                    $collection_name = $this->input->post('collection_name');
                    $department_name = $this->input->post('department_name');
                    //echo $department_name;exit;
                    $language = $this->input->post('language');
                    $csv_file = $_FILES['doc2']['tmp_name'];
                    $csv_content = file_get_contents($csv_file);
                    $file_data = $this->csvimport->get_array($csv_file);

                    //echo"<pre>";print_r($file_data);exit;

                    /********************************************************** */
                    $collection_name = getCollection($this->input->post('department_name'));
                    //echo "collection_name=".$collection_name;exit;
                    //$language = $this->input->post('language');
                    $this->db->trans_begin();

                    $id_list = array();
                    $intent_data = array();
                    //$j=0;
                    $k = 0;
                    $final_data = array();
                    foreach ($file_data as $key => $entry) {
                        /*$intent_data[$key]['intent_name'] = $file_data[$key]['intent_name']; // Add language key with value 2
                        $intent_data[$key]['collection_id_fk']=$department_name;
                        $intent_data[$key]['followup_yes'] = $file_data[$key]['followup_yes']; // Add language key with value 2
                        $intent_data[$key]['followup_no']=$file_data[$key]['followup_no'];
                        $intent_data[$key]['status']=1;*/


                        $intent_data = array(
                            'intent_name' => $file_data[$key]['intent_name'],
                            'collection_id_fk' => $department_name,
                            'followup_yes' => $file_data[$key]['followup_yes'],
                            'followup_no' => $file_data[$key]['followup_no'],
                            'status' => 1
                        );

                        $intent_id = $this->ChatbotEntryModel->insertIntent($intent_data);
                        if ($intent_id) {
                            array_push($id_list, $intent_id);
                            $question_data = array(
                                'intent_id_fk' => $intent_id,
                                'question' => $file_data[$key]['question'],
                                'answer' => $file_data[$key]['answer'],
                                'status' => 1,
                                'entry_date'    => date('Y-m-d H:i:sa'),
                                'entry_ip'      => $this->input->ip_address()

                            );

                            $question_id = $this->ChatbotEntryModel->insertQuestion($question_data);

                            if ($question_id) {
                                $this->db->trans_commit();
                                //to get data 
                                $send_data = $this->ChatbotEntryModel->getQuestionAnswerId($intent_id);
                                //echo"<pre>";print_r($send_data);exit;
                                $question_array = explode(";", $send_data['question']);
                                $question_array = array_map('trim', $question_array);
                                $send_data['question'] = $question_array;
                                //echo"<pre>";print_r($send_data);

                                //Making insert data
                                //$final_data = array();

                                for ($j = 0; $j < count($question_array); $j++) {

                                    $final_data[$k]['intent_name'] = $send_data['intent_name'];
                                    $final_data[$k]['intent_related_text'] = $send_data['question'][$j];
                                    $final_data[$k]['response'] = $send_data['answer'];
                                    $final_data[$k]['followup_yes'] = $send_data['followup_yes'];
                                    $final_data[$k]['followup_no'] = $send_data['followup_no'];
                                    $final_data[$k]['intent_id'] = $send_data['intent_id'];
                                    $k++;
                                }
                                //echo"<pre>";print_r($final_data);exit;
                                //Have to send this array to milvas
                                //$jdata = json_encode($final_data);
                            }
                        }
                    }

                    $intent_data = array(
                        'vdb_status' => 1

                    );

                    //$intentResult = $this->ChatbotEntryModel->updateChatbotIntentvdb_status($id_list, $intent_data);
                    //echo $intentResult;exit;
                    //echo"<pre>";print_r($id_list);exit;
                    $jdata = json_encode($final_data);
                    /*echo "<pre>";
                    print_r($final_data);
                    
                    $jdata = json_encode($final_data);
                    echo "<pre>";print_r($jdata);exit;*/

                    /************************************curl**************** */
                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'http://************:8010/upload_json/',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => '{
                        "collection_name": "' . $collection_name . '",
                        "data": ' . $jdata . '
                        }',
                        CURLOPT_HTTPHEADER => array(
                            'Content-Type: application/json'
                        ),
                    ));

                    $response = curl_exec($curl);

                    curl_close($curl);
                    //echo $response;exit;
                    $curl_res = json_decode($response, true);
                    $curl_result = $curl_res['result'];
                    // /************************************************************** */

                    if ($curl_result == "success") {

                        $intent_data = array(
                            'vdb_status' => 1

                        );

                        $intentResult = $this->ChatbotEntryModel->updateChatbotIntentvdb_status($id_list, $intent_data);

                        //$intentResult = $this->ChatbotEntryModel->updateChatbotIntent($intent_id, $intent_data);
                        $this->session->set_flashdata('success', 'Data is Saved in Local & Vector Database.');
                        redirect('chatbot_csv');
                    } else {
                        $this->session->set_flashdata('error', 'Data is Saved in Local Database but Not Saved in Vector Database');
                        redirect('chatbot_csv');
                    }


                    /********************************************************* */
                } else {
                    // Validation errors
                    $errors = validation_errors();
                    echo "Validation Error: " . $errors; // Debugging output
                    exit;
                }
            } else {
                $data['collection'] = $this->ChatbotEntryModel->get_department_collection();
                //echo "Form not submitted";
                //exit;
            }

            // Load the view with any error or success messages
            $this->load->view('super_admin/chatbot_csv_upload_view', $data);
        } else {
            // Redirect to login if not authenticated
            redirect(base_url() . 'login');
        }
    }

    public function questionAnswerList()
    {
        $data['admin'] = $this->admin;
        //echo"<pre>";print_r($data['admin']);exit;
        $this->load->library(array('auth'));
        if ($this->auth->sa_auth_chck() || $this->auth->nic_developer_auth_chck()) {
            if (!isset($_POST['submit'])) {

                $data['department'] = $this->ChatbotEntryModel->get_department();
                $data['collection'] = $this->ChatbotEntryModel->get_department_collection();

                //$data['qna']=$this->ChatbotEntryModel->getQuestionAnswerList();
                $this->load->view('super_admin/question_answer_list_view', $data);
            } else {
                /*$department_id=$this->input->post('department_id');
                echo json_encode($department_id);*/
            }
        } else {
            redirect(base_url() . 'login');
        }
    }

    public function getQuestionAnswerAjax()
    {
        $department_id = $this->input->post('department_id');
        //echo $department_id;exit;
        //echo json_encode($department_id);
        $qna = $this->ChatbotEntryModel->getQuestionAnswerList($department_id);
        //echo"<pre>";print_r($qna);exit;
        echo json_encode($qna);
    }

    public function questionAnswerEdit($id, $collectionName)
    {

        $data['admin'] = $this->admin;
        $this->load->library(array('auth'));

        // Check if the user is authenticated
        if ($this->auth->sa_auth_chck() || $this->auth->nic_developer_auth_chck()) {

            // If ID is provided, fetch the existing record for editing
            if ($id) {
                $this->session->set_userdata('content_id', $id);
                //echo $this->ChatbotEntryModel->getLanguageById($this->session->userdata('content_id'));exit;
                $data['question_answer'] = $this->ChatbotEntryModel->getQuestionAnswerId($id);
                $data['collection_name'] = $collectionName;
                //$data['keywords'] = $this->ChatbotEntryModel->get_keywords_by_chatbot_id($id);
                //echo"<pre>";print_r($data);exit;
                //echo"<pre>";print_r($data);exit;
            }

            // If form is submitted
            if ($this->input->post('submit')) {

                $this->form_validation->set_rules('intent_name', 'Intent Name', 'required');
                $this->form_validation->set_rules('question', 'Questions', 'required');
                $this->form_validation->set_rules('answer', 'Answer', 'required');

                if ($this->form_validation->run() == FALSE) {
                    $this->load->view('super_admin/chatbot_qna_edit_view', $data);
                } else {
                    //echo"<pre>";print_r($_POST);exit;

                    $language = $this->ChatbotEntryModel->getLanguageById($this->session->userdata('content_id'));

                    $collection_name = $this->input->post('collection_name');
                    $intent_name = $this->input->post('intent_name');
                    $question = $this->input->post('question');
                    $intent_id = $this->input->post('intent_id');
                    $answer = $this->input->post('answer');
                    $followup_yes = $this->input->post('followup_yes');
                    $followup_no = $this->input->post('followup_no');
                    $content_flag = 1;

                    //echo $collection_name;exit;

                    //echo $keyword;
                    /////////////////////////////////////////////////////////////////////////////
                    /********curl******* */
                    // $curl = curl_init();

                    // $postData = http_build_query([
                    //     'collection_name' => $collection_name,
                    //     'id' => $id,
                    //     'intent_name' => $intent_name,
                    //     'intent_related_text' => $question,
                    //     'response' => $answer,
                    //     'followup_yes' => $followup_yes,
                    //     'followup_no' => $followup_no
                    //     /*'entry_date'    => date('Y-m-d H:i:sa'),
                    //     'entry_ip'      => $this->input->ip_address()*/
                    // ]);
                    // // Manually build query string to avoid encoding the commas in 'keyword'
                    // $postDataString = 'collection_name=' . urlencode($collection_name) .
                    //     '&id=' . urlencode($id) .
                    //     '&intent_name=' . urlencode($intent_name) .
                    //     '&intent_related_text=' . $question .  // Add keyword without URL encoding
                    //     '&response=' . urlencode($answer);

                    // //echo"<pre>";print_r($postData);exit; 
                    // $pData = array('collection_name' => $collection_name, 'id' => $id, 'intent_name' => $intent_name, 'intent_related_text' => $question, 'followup_yes' => $followup_yes, 'followup_no' => $followup_no);
                    // //echo"<pre>";print_r($pData);exit;

                    // //echo "<pre>"; print_r($postDataString); exit;
                    // curl_setopt_array($curl, array(
                    //     CURLOPT_URL => 'http://************:8010/update_row',
                    //     //CURLOPT_URL => 'http://10.173.13.28:8066/update_row/',
                    //     CURLOPT_RETURNTRANSFER => true,
                    //     CURLOPT_ENCODING => '',
                    //     CURLOPT_MAXREDIRS => 10,
                    //     CURLOPT_TIMEOUT => 0,
                    //     CURLOPT_FOLLOWLOCATION => true,
                    //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    //     CURLOPT_CUSTOMREQUEST => 'POST',
                    //     CURLOPT_POSTFIELDS => array('collection_name' => $collection_name, 'id' => $id, 'intent_name' => $intent_name, 'intent_related_text' => $question, 'response' => $answer, 'followup_yes' => $followup_yes, 'followup_no' => $followup_no),
                    //     /*CURLOPT_HTTPHEADER => array(
                    //         'Content-Type: application/x-www-form-urlencoded'
                    //     ),*/
                    // ));

                    // $response = curl_exec($curl);

                    // curl_close($curl);
                    //echo $response;exit;
                    /******************* */
                    //$array_data = json_decode($response, true);
                    //echo "<pre>";
                    //print_r($array_data);
                    //exit;
                    //////////////////////////////////////////////////////////////////////

                    ///////////////////////////////////////////////////////////////////////
                    $send_data = $this->ChatbotEntryModel->getQuestionAnswerId($intent_id);
                    //echo"<pre>";print_r($send_data);exit;
                    $question_array = explode(";", $question);
                    $question_array = array_map('trim', $question_array);
                    $send_data['question'] = $question_array;
                    //echo"<pre>";print_r($send_data);
                    //Making insert data
                    $final_data = array();
                    for ($i = 0; $i < count($question_array); $i++) {
                        $final_data[$i]['intent_name'] = $intent_name;
                        $final_data[$i]['intent_related_text'] = $send_data['question'][$i];
                        $final_data[$i]['response'] = $answer;
                        $final_data[$i]['followup_yes'] = $followup_yes;
                        $final_data[$i]['followup_no'] = $followup_no;
                        $final_data[$i]['intent_id'] = $intent_id;
                    }
                    /*echo $collection_name;
                    echo"<pre>";print_r($final_data);exit;*/
                    //Have to send this array to milvas
                    $jdata = json_encode($final_data);
                    /************************************curl**************** */
                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'http://************:8010/update_json/',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => '{
                                "collection_name": "' . $collection_name . '",
                                "data": ' . $jdata . '
                                }',
                        CURLOPT_HTTPHEADER => array(
                            'Content-Type: application/json'
                        ),
                    ));

                    $response = curl_exec($curl);

                    curl_close($curl);
                    //echo $response;exit;
                    $curl_res = json_decode($response, true);
                    $curl_result = $curl_res['result'];
                    //////////////////////////////////////////////////////////////////////



                    //$array_data['result'] = 'success';
                    $this->db->trans_begin(); // Start Transaction

                    // Prepare data for insertion/updation
                    $intent_data = array(
                        'intent_name' => $this->input->post('intent_name'),
                        'followup_yes' => $this->input->post('followup_yes'),
                        'followup_no' => $this->input->post('followup_no')
                    );
                    $question_data = array(

                        'question'   => $this->input->post('question'),
                        'answer'   => $this->input->post('answer'),
                        'entry_date'    => date('Y-m-d H:i:sa'),
                        'entry_ip'      => $this->input->ip_address()
                    );
                    //echo"<pre>";print_r($data);exit;
                    if ($curl_res['result'] == 'success') {

                        // Update chatbot entry if ID is present (edit mode)
                        $question_result = $this->ChatbotEntryModel->update_question_answer_details($id, $question_data);
                        $intent_id_fk = $this->ChatbotEntryModel->getIntentIdFk($id);
                        if ($question_result && $intent_id_fk == $intent_id) {
                            $question_result = $this->ChatbotEntryModel->update_intent_name($intent_id_fk, $intent_data);
                            if ($question_result) {
                                $this->db->trans_commit();
                                $this->session->set_flashdata('success', 'Data is saved in database and also saved in vector DB.');
                                redirect('question_answer_list');
                            } else {
                                $this->db->trans_rollback();
                                $this->session->set_flashdata('error', 'Intentent name is not updated in database');
                                redirect('question_answer_list');
                            }
                        } else {
                            // Rollback if something went wrong
                            $this->db->trans_rollback();
                            $this->session->set_flashdata('error', 'Question and answer is not updated in database ');
                            redirect('question_answer_list');
                        }
                    } else {
                        $this->session->set_flashdata('error', 'Data is not saved in vector DB.');
                        redirect('question_answer_list');
                    }
                }
            } else {
                //echo "edit view";exit;
                // Load the view for adding or editing an entry
                $this->load->view('super_admin/chatbot_qna_edit_view', $data);
            }
        } else {
            redirect(base_url() . 'login');
        }
    }

    public function creationCollection()
    {

        $data['admin'] = $this->admin;
        //echo"<pre>";print_r($data['admin']);exit;
        $this->load->library(array('auth'));
        if ($this->auth->sa_auth_chck()) {
            if (!isset($_POST['submit'])) {
                //echo "hello";exit;
                $this->load->view('super_admin/chatbot_create_collection_view', $data);
            } else {
                // Set validation rules
                /*$this->form_validation->set_rules('question', 'Question', 'required');
                $this->form_validation->set_rules('answer', 'Answer', 'required');
                $this->form_validation->set_rules('keyword', 'Keyword', 'required');*/
                // You can uncomment the keyword validation if necessary
                // $this->form_validation->set_rules('keywords', 'Keywords', 'required');

                // If form validation fails
                //if ($this->form_validation->run() == FALSE) {
                //$this->session->set_flashdata('error', 'Enter Depertment Name');
                //$this->load->view('super_admin/chatbot_create_collection_view', $data);
                //} else {
                //echo"<pre>";print_r($this->input->post('language'));exit;
                /***********************************************18-2-25**************** */

                if (checkDepartmentName($this->input->post('depertment_name')) > 0) {
                    $this->session->set_flashdata('error', 'Enter Diffrent Depertment Name');
                    redirect('chatbot_create_collection');
                }

                $lan = $this->input->post('language');
                //echo"<pre>";print_r($lan);exit;
                $this->db->trans_begin();

                foreach ($lan as $l) {

                    if ($l == '1') { //english
                        //echo"english";
                        //$department_n=$this->input->post('depertment_name')."_eng";
                        $data = array(
                            'department_name' => $this->input->post('depertment_name'),
                            'language' => $l
                        );
                        $collection_id = $this->ChatbotEntryModel->insert_depertment($data); //transaction1

                        if ($collection_id) {
                            $this->db->trans_commit();
                            /*$curl = curl_init();
                            curl_setopt_array($curl, array(
                              CURLOPT_URL=>'https://wb-gateway.napix.gov.in/wb/coeailabkol/chatbot_generate_collection_name/',  
                            //CURLOPT_URL => 'http://************:8010/generate_collection_name/',
                            //CURLOPT_URL => 'http://10.173.13.28:8063/generate_collection_name/',
                            CURLOPT_RETURNTRANSFER => true,
                            CURLOPT_ENCODING => '',
                            CURLOPT_MAXREDIRS => 10,
                            CURLOPT_TIMEOUT => 0,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                            CURLOPT_CUSTOMREQUEST => 'POST',
                            ));
        
                            $response = curl_exec($curl);
        
                            curl_close($curl);*/

                            /*$curl = curl_init();

                            curl_setopt_array($curl, array(
                                CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/chatbot_generate_collection_name/',
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_ENCODING => '',
                                CURLOPT_MAXREDIRS => 10,
                                CURLOPT_TIMEOUT => 0,
                                CURLOPT_FOLLOWLOCATION => true,
                                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                CURLOPT_CUSTOMREQUEST => 'POST',
                                CURLOPT_HTTPHEADER => array(
                                    'client_id: 7f156c424b829df15beb9b8803f3a80a',
                                    'client_secret: 2a2310f9c49cb8ae679367304b495f95'
                                ),
                            ));

                            $response = curl_exec($curl);

                            curl_close($curl);*/
                            //echo $response;


                            $curl = curl_init();

                            curl_setopt_array($curl, array(
                                CURLOPT_URL => 'http://************:8012/generate_collection_name/',
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_ENCODING => '',
                                CURLOPT_MAXREDIRS => 10,
                                CURLOPT_TIMEOUT => 0,
                                CURLOPT_FOLLOWLOCATION => true,
                                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                CURLOPT_CUSTOMREQUEST => 'POST',
                            ));

                            $response = curl_exec($curl);

                            curl_close($curl);
                            //echo $response;


                            $array_data = json_decode($response, true);
                            //echo"<pre>";print_r($array_data);exit;
                        }

                        $collection_name = $array_data['collection_name'];
                        $result = $array_data['result'];
                        $data = array(
                            'collection_name' => $collection_name,
                            'status' => 1
                        );
                        $eng_collection = $collection_name;
                        $insert_collection_name = $this->ChatbotEntryModel->generateCollection($collection_id, $data);
                    }
                    if ($l == '2') { //bengali
                        //echo"ben";exit;
                        //echo "bengali";
                        $data = array(
                            'department_name' => $this->input->post('depertment_name'),
                            'language' => $l
                        );
                        $collection_id = $this->ChatbotEntryModel->insert_depertment($data); //transaction1

                        if ($collection_id) {
                            $this->db->trans_commit();
                            /*$curl = curl_init();
                            curl_setopt_array($curl, array(
                            CURLOPT_URL=>'https://wb-gateway.napix.gov.in/wb/coeailabkol/chatbot_generate_collection_name/',
                            //CURLOPT_URL => 'http://************:8010/generate_collection_name/',
                            //CURLOPT_URL => 'http://10.173.13.28:8063/generate_collection_name/',
                            CURLOPT_RETURNTRANSFER => true,
                            CURLOPT_ENCODING => '',
                            CURLOPT_MAXREDIRS => 10,
                            CURLOPT_TIMEOUT => 0,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                            CURLOPT_CUSTOMREQUEST => 'POST',
                            ));
        
                            $response = curl_exec($curl);
        
                            curl_close($curl);*/

                            $curl = curl_init();

                            curl_setopt_array($curl, array(
                                CURLOPT_URL => 'https://wb-gateway.napix.gov.in/wb/coeailabkol/chatbot_generate_collection_name/',
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_ENCODING => '',
                                CURLOPT_MAXREDIRS => 10,
                                CURLOPT_TIMEOUT => 0,
                                CURLOPT_FOLLOWLOCATION => true,
                                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                CURLOPT_CUSTOMREQUEST => 'POST',
                                CURLOPT_HTTPHEADER => array(
                                    'client_id: 7f156c424b829df15beb9b8803f3a80a',
                                    'client_secret: 2a2310f9c49cb8ae679367304b495f95'
                                ),
                            ));

                            $response = curl_exec($curl);

                            curl_close($curl);
                            //echo $response;

                            $array_data = json_decode($response, true);
                        }

                        $collection_name = $array_data['collection'];
                        $result = $array_data['result'];
                        $data = array(
                            'collection_name' => $collection_name,
                        );
                        $ben_collection = $collection_name;
                        $insert_collection_name = $this->ChatbotEntryModel->generateCollection($collection_id, $data);
                    }
                    if ($l == 3) { //hindi
                        echo "hindi";
                        exit;
                    }

                    if ($insert_collection_name && $result == 'success') {
                        $this->db->trans_commit();
                        $this->session->set_flashdata('success', 'Collection name is/are ' . $eng_collection . ' ' . $ben_collection);
                        redirect('chatbot_create_collection');
                    } else {
                        $this->db->trans_rollback();
                        $this->session->set_flashdata('error', 'Collection name is not generated');
                        redirect('chatbot_create_collection');
                    }
                }
                /********************************************************************** */
                /*$language=$this->input->post('language');
                $this->db->trans_begin();
                // Insert document data
                if(checkDepartmentName($this->input->post('depertment_name'))>0)
                {
                    $this->session->set_flashdata('error', 'Enter Diffrent Depertment Name');
                        redirect('chatbot_create_collection');
                }
                $data = array(
                    'department_name' => $this->input->post('depertment_name'),
                    'language'=>$language
                );
                $collection_id = $this->ChatbotEntryModel->insert_depertment($data); //transaction1

                if($collection_id){
                    $this->db->trans_commit();
                    if($language=='1'){//english
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                    CURLOPT_URL => 'http://************:8010/generate_collection_name/',
                    //CURLOPT_URL => 'http://10.173.13.28:8063/generate_collection_name/',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    ));

                    $response = curl_exec($curl);

                    curl_close($curl);
                   
                    $array_data = json_decode($response, true);
                    }
                    elseif($language=='2')//bengali
                    {
                        $curl = curl_init();
                    curl_setopt_array($curl, array(
                    CURLOPT_URL => 'http://************:8010/generate_collection_name/',
                    //CURLOPT_URL => 'http://10.173.13.28:8063/generate_collection_name/',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    ));

                    $response = curl_exec($curl);

                    curl_close($curl);
                    
                    $array_data = json_decode($response, true);
                    }
                    
                    $collection_name=$array_data['collection'];
                    $result=$array_data['result'];
                    $data = array(
                        'collection_name' => $collection_name,
                    );
                    $insert_collection_name=$this->ChatbotEntryModel->generateCollection($collection_id,$data);
                    if($insert_collection_name && $result=='success'){
                        $this->session->set_flashdata('success', 'Collection name is '.$collection_name);
                        redirect('chatbot_create_collection');
                    }
                    else{
                        $this->session->set_flashdata('error', 'Collection name is not generated');
                        redirect('chatbot_create_collection');
                    }

                }*/
            }
            //}
        } else {
            redirect(base_url() . 'login');
        }
    }



    /*******************************************social registry*********** */

    //AI Sanlaap

    public function aiSanlaapSocialRegistry()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_social_registry_view', $data);
    }





    public function chatFormSubmitSocialRegistry()
    {
        //echo"From chatFormSubmit";exit;
        //$name = $this->input->post('name');
        $dob = $this->input->post('dob');
        $gender = $this->input->post('gender');
        $marital_status = $this->input->post('marital_status');
        $social_category = $this->input->post('social_category');
        $income = $this->input->post('income');
        $data_res = "dob:" . $dob . ",gender:" . $gender . ",marital_status:" . $marital_status . ",caste:" . $social_category . ",annual_income:" . $income;
        //echo $data_res;exit;

        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://************:8012/predict',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
             "session_id": "' . $session_id . '",
             "collection_id": "",
             "user_response": "' . $data_res . '",
             "response_type": "form",
             "followup_yes": "",
             "followup_no": "",
             "scheme_id": "",
             "department_id": "",
             "criteria_list":[""]
             }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $res = json_decode($response, true);
        $criteria_list = $res['criteria_list'];
        //echo $criteria_list;exit;
        //echo"<pre>";print_r($res['criteria_list']);exit;
        $this->session->set_userdata('criteria_list', $criteria_list);
        $this->session->set_userdata('intent_name', $res['intent_name']);

        //echo"<pre>";print_r($this->session->userdata('criteria_list'));
        curl_close($curl);
        echo $response;
    }




    public function sessionCreateSocialRegistry()
    {

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://************:8012/session_id_generator',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            /*CURLOPT_POSTFIELDS => '{
             "session_id_params": {
                 "session_type": "session_create",
             }
             }',*/
            CURLOPT_POSTFIELDS => '{
                 "session_id_params": {
                   "session_type": "session_create",
                   "session_id": "NA"
                 }
             }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        /*echo $response;
         exit;*/
        $res = json_decode($response, true);
        echo "response=" . $response;
        echo "session_id=" . $res['session_id'];
        $this->session->set_userdata('session_id', $res['session_id']);
        //$this->session->set_userdata('followup_no', $res['followup_no']);


    }

    public function sessionDestroySocialRegistry()
    {
        //echo"destroy";exit;
        //echo"<pre>";print_r($_SESSION);exit;
        $session_id = $this->session->userdata('session_id');
        if ($this->session->has_userdata('session_id')) {
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://************:8012/session_id_generator',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                 "session_id_params": {
                   "session_type": "session_destroy",
                   "session_id": "' . $session_id . '"
                 }
             }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            /*echo $response;
         exit;*/
            $res = json_decode($response, true);

            $this->session->unset_userdata('session_id');

            echo "response=" . $response;
        }
    }

    public function aiSanlaapBotQuestionAnswerSocialRegistry()
    {

        $collection_id = "collection_1d45baab_4522_420c_a149_c5d1c321fc28";
        $question = $this->input->post('question');
        $language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        /*if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
             $followup_yes = $this->session->userdata('followup_yes');
             $followup_no = $this->session->userdata('followup_no');
         }*/
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

        if ($language == "1") { //english

            $response_type = "";


            $type_array = ["check my eligibility", "eligibility"];

            // Convert all array values to lowercase
            $lower_type_array = array_map('strtolower', $type_array);

            // Convert the input to lowercase and check
            /*if (in_array(strtolower($question), $lower_type_array)) {
                $response_type="check_eligibility";
            } */
            //echo "question=".$question;
            // if ($question == "check my eligibility") {
            //     $response_type = "check_eligibility";

            //     $data = array(
            //         "session_id" => "'.$session_id.'",
            //         "collection_id" => "NA",
            //         "user_response" => "'.$question.'",
            //         "response_type" => "'.$response_type.'",
            //         "followup_yes" => "NA",
            //         "followup_no" => "NA",
            //         "scheme_id" => "NA",
            //         "department_id" => "NA",
            //         "criteria_list" => array("NA")
            //     );

            //     $payload = json_encode($data);
            // } else {
            //     $response_type = "text";

            //     $data = array(
            //         "session_id" => "",
            //         "collection_id" => "'.$collection_id.'",
            //         "user_response" => "'.$question.'",
            //         "response_type" => "'.$response_type.'",
            //         "followup_yes" => "",
            //         "followup_no" => "",
            //         "scheme_id" => "NA",
            //         "department_id" => "NA",
            //         "criteria_list" => array()
            //     );

            //     $payload = json_encode($data);
            // }

            // /*echo "response type=" . $response_type;
            // exit;*/


            // $curl = curl_init();

            // curl_setopt_array($curl, array(
            //     CURLOPT_URL => 'http://************:8012/predict',
            //     CURLOPT_RETURNTRANSFER => true,
            //     CURLOPT_ENCODING => '',
            //     CURLOPT_MAXREDIRS => 10,
            //     CURLOPT_TIMEOUT => 0,
            //     CURLOPT_FOLLOWLOCATION => true,
            //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            //     CURLOPT_CUSTOMREQUEST => 'POST',
            //     /*CURLOPT_POSTFIELDS => '{
            //      "session_id": "' . $session_id . '",
            //      "collection_id": "",
            //      "user_response": "' . $question . '",
            //      "response_type": "check_eligibility",
            //      "followup_yes": "",
            //      "followup_no": "",
            //      "scheme_id": "",
            //      "department_id": "",
            //      "criteria_list":[""]
            //      }',*/
            //     /*CURLOPT_POSTFIELDS => '{
            //         "session_id": "' . $session_id . '",
            //         "collection_id": "NA",
            //         "user_response": "' . $question . '",
            //         "response_type": "' . $response_type . '",
            //         "followup_yes": "NA",
            //         "followup_no": "NA",
            //         "scheme_id": "NA",
            //         "department_id": "NA",
            //         "criteria_list": [
            //           "NA"
            //         ]
            //       }',*/
            //     /*CURLOPT_POSTFIELDS => '{
            //         "session_id": "",
            //         "collection_id": "collection_1d45baab_4522_420c_a149_c5d1c321fc28",
            //         "user_response": "kanyashree eligible",
            //         "response_type": "text",
            //         "followup_yes": "",
            //         "followup_no": "",
            //         "scheme_id": "NA",
            //         "department_id": "NA",
            //         "criteria_list":[]
            //       }',*/
            //     CURLOPT_POSTFIELDS => $payload,
            //     CURLOPT_HTTPHEADER => array(
            //         'Content-Type: application/json'
            //     ),
            // ));

            // $response = curl_exec($curl);


            if ($question == "check my eligibility") {
                $response_type = "check_eligibility";

                $data = array(
                    "session_id" => $session_id,
                    "collection_id" => "NA",
                    "user_response" => $question,
                    "response_type" => $response_type,
                    "followup_yes" => "NA",
                    "followup_no" => "NA",
                    "scheme_id" => "NA",
                    "department_id" => "NA",
                    "criteria_list" => array("NA")
                );
            } else {
                $response_type = "text";

                $data = array(
                    "session_id" => $session_id,
                    "collection_id" => $collection_id,
                    "user_response" => $question,
                    "response_type" => $response_type,
                    "followup_yes" => "",
                    "followup_no" => "",
                    "scheme_id" => "NA",
                    "department_id" => "NA",
                    "criteria_list" => array()
                );
            }

            $payload = json_encode($data);

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://************:8012/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $payload,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            if (curl_errno($curl)) {
                echo 'Curl error: ' . curl_error($curl);
            } else {
                echo $response;
            }
            curl_close($curl);

            //curl_close($curl);
            //$res = json_decode($response, true);
            //echo $response;
        }
    }

    public function schemeBucketSocialRegistry()
    {


        $question = $this->input->post('scheme_bucket');
        $intent_name = $this->session->userdata('intent_name');
        //echo "intent_name=".$intent_name;
        //echo "question=".$question;
        $res_data = $intent_name . ":" . $question;
        //echo "res_data=".$res_data;
        //echo "criteria_list=".json_encode($this->session->userdata('criteria_list'));
        //$res_data = "scheme_bucket:".$question;
        //echo "scheme_bucket=".$res_data;
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
            $criteria_list = $this->session->userdata('criteria_list');
            $criteria_list_json = json_encode($criteria_list);
            //echo "session_id=".$session_id;
            //echo"criteria_list="."<pre>";print_r($criteria_list);exit;

            /*}
 
         if ($session_id) { */

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://************:8012/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                     "session_id": "' . $session_id . '",
                     "collection_id": "",
                     "user_response": "' . $res_data . '",
                     "response_type": "options",
                     "followup_yes": "",
                     "followup_no": "",
                     "scheme_id": "",
                     "department_id": "",
                     "criteria_list": ' . $criteria_list_json . '
                   }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $res = json_decode($response, true);
            //echo"<pre>";print_r($res);
            $this->session->set_userdata('intent_name', $res['intent_name']);
            $this->session->set_userdata('criteria_list', $res['criteria_list']);

            echo $response;
            //exit;
        } else {
            echo "Empty Session";
        }
    }


    /*******************************************E-nathikaran chatbot******************** */

    //AI Sanlaap

    public function aiSanlaapENathikaran()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_e_nathikaran_view', $data);
    }





    public function chatFormSubmitENathikaran()
    {
        //echo"From chatFormSubmit";exit;
        //$name = $this->input->post('name');
        $dob = $this->input->post('dob');
        $gender = $this->input->post('gender');
        $marital_status = $this->input->post('marital_status');
        $social_category = $this->input->post('social_category');
        $income = $this->input->post('income');
        $data_res = "dob:" . $dob . ",gender:" . $gender . ",marital_status:" . $marital_status . ",caste:" . $social_category . ",annual_income:" . $income;
        //echo $data_res;exit;

        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://************:8012/predict',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
             "session_id": "' . $session_id . '",
             "collection_id": "",
             "user_response": "' . $data_res . '",
             "response_type": "form",
             "followup_yes": "",
             "followup_no": "",
             "scheme_id": "",
             "department_id": "",
             "criteria_list":[""]
             }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $res = json_decode($response, true);
        $criteria_list = $res['criteria_list'];
        //echo $criteria_list;exit;
        //echo"<pre>";print_r($res['criteria_list']);exit;
        $this->session->set_userdata('criteria_list', $criteria_list);
        $this->session->set_userdata('intent_name', $res['intent_name']);

        //echo"<pre>";print_r($this->session->userdata('criteria_list'));
        curl_close($curl);
        echo $response;
    }




    public function sessionCreateENathikaran()
    {

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://************:8012/session_id_generator',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            /*CURLOPT_POSTFIELDS => '{
             "session_id_params": {
                 "session_type": "session_create",
             }
             }',*/
            CURLOPT_POSTFIELDS => '{
                 "session_id_params": {
                   "session_type": "session_create",
                   "session_id": "NA"
                 }
             }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        /*echo $response;
         exit;*/
        $res = json_decode($response, true);
        echo "response=" . $response;
        echo "session_id=" . $res['session_id'];
        $this->session->set_userdata('session_id', $res['session_id']);
        //$this->session->set_userdata('followup_no', $res['followup_no']);


    }

    public function sessionDestroyENathikaran()
    {
        //echo"destroy";exit;
        //echo"<pre>";print_r($_SESSION);exit;
        $session_id = $this->session->userdata('session_id');
        if ($this->session->has_userdata('session_id')) {
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://************:8012/session_id_generator',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                 "session_id_params": {
                   "session_type": "session_destroy",
                   "session_id": "' . $session_id . '"
                 }
             }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            /*echo $response;
         exit;*/
            $res = json_decode($response, true);

            $this->session->unset_userdata('session_id');

            echo "response=" . $response;
        }
    }

    public function aiSanlaapBotQuestionAnswerENathikaran()
    {

        $collection_id = "collection_1d45baab_4522_420c_a149_c5d1c321fc28";
        $question = $this->input->post('question');
        $language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        /*if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
             $followup_yes = $this->session->userdata('followup_yes');
             $followup_no = $this->session->userdata('followup_no');
         }*/
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

        if ($language == "1") { //english

            $response_type = "";


            $type_array = ["check my eligibility", "eligibility"];

            // Convert all array values to lowercase
            $lower_type_array = array_map('strtolower', $type_array);




            if ($question == "check my eligibility") {
                $response_type = "check_eligibility";

                $data = array(
                    "session_id" => $session_id,
                    "collection_id" => "NA",
                    "user_response" => $question,
                    "response_type" => $response_type,
                    "followup_yes" => "NA",
                    "followup_no" => "NA",
                    "scheme_id" => "NA",
                    "department_id" => "NA",
                    "criteria_list" => array("NA")
                );
            } else {
                $response_type = "text";
 
                $data = array(
                    "session_id" => $session_id,
                    "collection_id" => $collection_id,
                    "user_response" => $question,
                    "response_type" => $response_type,
                    "followup_yes" => "",
                    "followup_no" => "",
                    "scheme_id" => "NA",
                    "department_id" => "NA",
                    "criteria_list" => array()
                );
            }

            $payload = json_encode($data);

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://************:8012/predict',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $payload,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            if (curl_errno($curl)) {
                echo 'Curl error: ' . curl_error($curl);
            } else {
                echo $response;
            }
            curl_close($curl);

            //curl_close($curl);
            //$res = json_decode($response, true);
            //echo $response;
        }
    }

    public function schemeBucketENathikaran()
    {


        $question = $this->input->post('scheme_bucket');
        $intent_name = $this->session->userdata('intent_name');
        //echo "intent_name=".$intent_name;
        //echo "question=".$question;
        $res_data = $intent_name . ":" . $question;
        //echo "res_data=".$res_data;
        //echo "criteria_list=".json_encode($this->session->userdata('criteria_list'));
        //$res_data = "scheme_bucket:".$question;
        //echo "scheme_bucket=".$res_data;
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
            $criteria_list = $this->session->userdata('criteria_list');
            $criteria_list_json = json_encode($criteria_list);


            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http:// 172.18.97.170:8020/chatbot/step',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                     "session_id": "' . $session_id . '",
                     "collection_id": "",
                     "user_response": "' . $res_data . '",
                     "response_type": "options",
                     "followup_yes": "",
                     "followup_no": "",
                     "scheme_id": "",
                     "department_id": "",
                     "criteria_list": ' . $criteria_list_json . '
                   }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $res = json_decode($response, true);
            //echo"<pre>";print_r($res);
            $this->session->set_userdata('intent_name', $res['intent_name']);
            $this->session->set_userdata('criteria_list', $res['criteria_list']);

            echo $response;
            //exit;
        } else {
            echo "Empty Session";
        }
    }

    /***************************E-Nathikaran bot 2************************ */
    

    public function aiSanlaapENathikaran2()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_e_nathikaran2_view', $data);
    }

  
    public function aiSanlaapBotENathikaranQuestionAnswer2()
    {
        //echo"AI Sanlaap";exit;
        $question = $this->input->post('question');
        //$language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
            $followup_yes = $this->session->userdata('followup_yes');
            $followup_no = $this->session->userdata('followup_no');
        }

        if ($language == "1") { //english
            $curl = curl_init();

            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://10.173.13.28:8062/predict',
                // CURLOPT_URL => 'http://************:8010/predict',
                CURLOPT_URL => 'http://************:8010/e_nathikaran',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_18547653_e125_48fd_b9b6_821d18f90f72", "followup_yes" => $followup_yes, "followup_no" => $followup_no)), //HOI
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_30a694db_b472_452c_9901_e202e6ff9304", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//DPMU
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "collection_c9666ef5_8d6e_44bf_a5da_adda9d0732dd", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO
                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question, "department" => "'.$collection.'", "followup_yes" => $followup_yes, "followup_no" => $followup_no)),//BDO/SDO


                //CURLOPT_POSTFIELDS => json_encode(array("question" => $question,"department" => "Rupashree")),  // Ensure the question is JSON-encoded

                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            /*if ($response === false) {
             echo "cURL Error: " . curl_error($curl);
         } exit;*/

            curl_close($curl);
            $res = json_decode($response, true);
            $this->session->set_userdata('followup_yes', $res['followup_yes']);
            $this->session->set_userdata('followup_no', $res['followup_no']);


            //echo"<pre>";print_r($res);exit;
            echo $response;
        }
    }
//     /*******************************************EoDB chatbot******************** */

//     //AI Sanlaap

//     public function aiSanlaapEODB()
//     {
//         $this->load->library(array('auth'));
//         $data['admin'] = $this->admin;
//         $this->load->view('super_admin/sanlaap_bot_eodb_view', $data);
//     }





//     public function chatFormSubmitEODB()
//     {
//         //echo"From chatFormSubmit";exit;
//         //$name = $this->input->post('name');
//         $dob = $this->input->post('dob');
//         $gender = $this->input->post('gender');
//         $marital_status = $this->input->post('marital_status');
//         $social_category = $this->input->post('social_category');
//         $income = $this->input->post('income');
//         $data_res = "dob:" . $dob . ",gender:" . $gender . ",marital_status:" . $marital_status . ",caste:" . $social_category . ",annual_income:" . $income;
//         //echo $data_res;exit;

//         $session_id = "";
//         if ($this->session->has_userdata('session_id')) {
//             $session_id = $this->session->userdata('session_id');
//         }

//         $curl = curl_init();

//         curl_setopt_array($curl, array(
//             CURLOPT_URL => 'http://************:8012/predict',
//             CURLOPT_RETURNTRANSFER => true,
//             CURLOPT_ENCODING => '',
//             CURLOPT_MAXREDIRS => 10,
//             CURLOPT_TIMEOUT => 0,
//             CURLOPT_FOLLOWLOCATION => true,
//             CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//             CURLOPT_CUSTOMREQUEST => 'POST',
//             CURLOPT_POSTFIELDS => '{
//              "session_id": "' . $session_id . '",
//              "collection_id": "",
//              "user_response": "' . $data_res . '",
//              "response_type": "form",
//              "followup_yes": "",
//              "followup_no": "",
//              "scheme_id": "",
//              "department_id": "",
//              "criteria_list":[""]
//              }',
//             CURLOPT_HTTPHEADER => array(
//                 'Content-Type: application/json'
//             ),
//         ));

//         $response = curl_exec($curl);
//         $res = json_decode($response, true);
//         $criteria_list = $res['criteria_list'];
//         //echo $criteria_list;exit;
//         //echo"<pre>";print_r($res['criteria_list']);exit;
//         $this->session->set_userdata('criteria_list', $criteria_list);
//         $this->session->set_userdata('intent_name', $res['intent_name']);

//         //echo"<pre>";print_r($this->session->userdata('criteria_list'));
//         curl_close($curl);
//         echo $response;
//     }




//     public function sessionCreateEODB()
//     {

//         // $curl = curl_init();

//         // curl_setopt_array($curl, array(
//         //     CURLOPT_URL => 'http://************:8012/session_id_generator',
//         //     CURLOPT_RETURNTRANSFER => true,
//         //     CURLOPT_ENCODING => '',
//         //     CURLOPT_MAXREDIRS => 10,
//         //     CURLOPT_TIMEOUT => 0,
//         //     CURLOPT_FOLLOWLOCATION => true,
//         //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//         //     CURLOPT_CUSTOMREQUEST => 'POST',
//         //     /*CURLOPT_POSTFIELDS => '{
//         //      "session_id_params": {
//         //          "session_type": "session_create",
//         //      }
//         //      }',*/
//         //     CURLOPT_POSTFIELDS => '{
//         //          "session_id_params": {
//         //            "session_type": "session_create",
//         //            "session_id": "NA"
//         //          }
//         //      }',
//         //     CURLOPT_HTTPHEADER => array(
//         //         'Content-Type: application/json'
//         //     ),
//         // ));

//         // $response = curl_exec($curl);

//         // curl_close($curl);
//         /*echo $response;
//          exit;*/
         

//         $curl = curl_init();

//         curl_setopt_array($curl, array(
//         CURLOPT_URL => 'http://127.0.0.1:8022/session_manager',
//         CURLOPT_RETURNTRANSFER => true,
//         CURLOPT_ENCODING => '',
//         CURLOPT_MAXREDIRS => 10,
//         CURLOPT_TIMEOUT => 0,
//         CURLOPT_FOLLOWLOCATION => true,
//         CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//         CURLOPT_CUSTOMREQUEST => 'POST',
//         CURLOPT_POSTFIELDS =>'{
//         "session_type": "session_create",
//         "session_id": ""
//         }
//         ',
//         CURLOPT_HTTPHEADER => array(
//             'Content-Type: application/json'
//         ),
//         ));

//         $response = curl_exec($curl);

//         curl_close($curl);
//         //echo $response;

//         $res = json_decode($response, true);
//         echo "response=" . $response;
//         echo "session_id=" . $res['session_id'];
//         $this->session->set_userdata('session_id', $res['session_id']);
//         //$this->session->set_userdata('followup_no', $res['followup_no']);


//     }

//     public function sessionDestroyEODB()
//     {
//         if ($this->session->has_userdata('step')) {
//             $this->session->unset_userdata('step');

//         }
//         if ($this->session->has_userdata('session_flag')) {
//             $this->session->unset_userdata('session_flag');

//         }
//         //echo"destroy";exit;
//         //echo"<pre>";print_r($_SESSION);exit;
//         $session_id = $this->session->userdata('session_id');
//         if ($this->session->has_userdata('session_id')) {
//             // $curl = curl_init();

//             // curl_setopt_array($curl, array(
//             //     CURLOPT_URL => 'http://************:8012/session_id_generator',
//             //     CURLOPT_RETURNTRANSFER => true,
//             //     CURLOPT_ENCODING => '',
//             //     CURLOPT_MAXREDIRS => 10,
//             //     CURLOPT_TIMEOUT => 0,
//             //     CURLOPT_FOLLOWLOCATION => true,
//             //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//             //     CURLOPT_CUSTOMREQUEST => 'POST',
//             //     CURLOPT_POSTFIELDS => '{
//             //      "session_id_params": {
//             //        "session_type": "session_destroy",
//             //        "session_id": "' . $session_id . '"
//             //      }
//             //  }',
//             //     CURLOPT_HTTPHEADER => array(
//             //         'Content-Type: application/json'
//             //     ),
//             // ));

//             // $response = curl_exec($curl);

//             // curl_close($curl);
//             /*echo $response;
//             exit;*/

//             $curl = curl_init();

//             curl_setopt_array($curl, array(
//             CURLOPT_URL => 'http://127.0.0.1:8022/session_manager',
//             CURLOPT_RETURNTRANSFER => true,
//             CURLOPT_ENCODING => '',
//             CURLOPT_MAXREDIRS => 10,
//             CURLOPT_TIMEOUT => 0,
//             CURLOPT_FOLLOWLOCATION => true,
//             CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//             CURLOPT_CUSTOMREQUEST => 'POST',
//             CURLOPT_POSTFIELDS =>'{
//             "session_type": "session_destroy",
//             "session_id": "073641bf-0acd-4cc8-9ebd-c6afb45a61d3"
//             }
//             ',
//             CURLOPT_HTTPHEADER => array(
//                 'Content-Type: application/json'
//             ),
//             ));

//             $response = curl_exec($curl);

//             curl_close($curl);
//             //echo $response;

//             $res = json_decode($response, true);

//             $this->session->unset_userdata('session_id');

//             echo "response=" . $response;
//         }
//     }

//     public function aiSanlaapBotQuestionAnswerEODB()
//     {

//         $collection_id = "collection_4fcd8a1d_bfa1_42f1_b7c4_b986018efc68";
//         $question = $this->input->post('question');
//         $language = $this->input->post('language');
//         $language = "1";
//         $followup_yes = "";
//         $followup_no = "";
//         $caption="";
//         $value="";
//         $step_id = "";
//         $next_step=1;
//         $user_input="Choose sector or industry";
//         /*if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
//              $followup_yes = $this->session->userdata('followup_yes');
//              $followup_no = $this->session->userdata('followup_no');
//          }*/
//         $session_id = "";
//         if ($this->session->has_userdata('session_id')) {
//             $session_id = $this->session->userdata('session_id');
//         }

            
//             if ($this->session->has_userdata('step')) {
//             $step_id = $this->session->userdata('step');
//             if($step_id==1){
//                 $caption="sector";
//                 $value=$question;
//                 $next_step=2;
//                 $user_input=$question;
//             }
//             elseif($step_id==2){
//                 $caption="investment";
//                 $value=$question;
//                 $next_step=3;
//                 $user_input=$question;
//             }
//             elseif($step_id==3){
//                 $caption="application_type";
//                 $value=$question;
//                 $next_step=4;
//                 $user_input=$question;
                
//             }
//             /*elseif($step_id==3 && $question=="Do you want to ask general queries?"){
//                 $caption="application_type";
//                 $value=$question;
//                 $next_step=null;
//                 $user_input=$question;//this will only works here
                
//             }*/

//             elseif($step_id==4 && $this->session->has_userdata('session_flag')){
//                 $caption="application_type";
//                 $value=$this->session->userdata('session_flag');
//                 $next_step=4;
//                 $user_input=$question;//this will only works here
//                 //echo $value;exit;
//             }

//             elseif($step_id==4 && $question=="Pre-operational"){
//                 $caption="service_type";
//                 $value=$question;
//                 $next_step=5;
//                 $user_input=$question;
                
//             }
//             elseif($step_id==4 && $question=="Pre-establishment"){
//                 $caption="service_type";
//                 $value=$question;
//                 $next_step=5;
//                 $user_input=$question;
                
//             }
//             elseif($step_id==5){
//                 echo"done";exit;
                
//             }
//             //elseif($question==""){}
            
            
//             }
    

//             $curl = curl_init();

//             curl_setopt_array($curl, array(
//             CURLOPT_URL => 'http://127.0.0.1:8022/chatbot/step',
//             CURLOPT_RETURNTRANSFER => true,
//             CURLOPT_ENCODING => '',
//             CURLOPT_MAXREDIRS => 10,
//             CURLOPT_TIMEOUT => 0,
//             CURLOPT_FOLLOWLOCATION => true,
//             CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//             CURLOPT_CUSTOMREQUEST => 'POST',
//             CURLOPT_POSTFIELDS =>'{
//             "session_id": "'.$session_id.'",
//             "collection_name":"collection_4fcd8a1d_bfa1_42f1_b7c4_b986018efc68",
//             "user_input": "'.$user_input.'",
//             "step": '.$next_step.',
//             "user_response": {
//                 "caption": "'.$caption.'",
//                 "value": "'.$value.'"
//             },
//             "response_type": "",
//             "followup_yes": "",
//             "followup_no": "",
//             "criteria_list": []
//             }',
//             CURLOPT_HTTPHEADER => array(
//                 'Content-Type: application/json'
//             ),
//             ));

//             $response = curl_exec($curl);

//             curl_close($curl);
//             $res = json_decode($response, true);
//             //$session_step=$res['step'];
//             //echo"<pre>";print_r($res);exit;
//             $this->session->unset_userdata('step');
//             $this->session->set_userdata('step', $res['step']);
//             if($res['intent_name']=="ai_response"){
//                 $this->session->set_userdata('session_flag', "Do you want to ask general queries?");
//             }
//             echo $response;


        
//     }

//     public function schemeBucketEODB()
//     {


//         $question = $this->input->post('scheme_bucket');
//         $intent_name = $this->session->userdata('intent_name');
//         //echo "intent_name=".$intent_name;
//         //echo "question=".$question;
//         $res_data = $intent_name . ":" . $question;
//         //echo "res_data=".$res_data;
//         //echo "criteria_list=".json_encode($this->session->userdata('criteria_list'));
//         //$res_data = "scheme_bucket:".$question;
//         //echo "scheme_bucket=".$res_data;
//         $session_id = "";
//         if ($this->session->has_userdata('session_id')) {
//             $session_id = $this->session->userdata('session_id');
//             $criteria_list = $this->session->userdata('criteria_list');
//             $criteria_list_json = json_encode($criteria_list);


//             $curl = curl_init();

//             curl_setopt_array($curl, array(
//                 CURLOPT_URL => 'http:// 172.18.97.170:8020/chatbot/step',
//                 CURLOPT_RETURNTRANSFER => true,
//                 CURLOPT_ENCODING => '',
//                 CURLOPT_MAXREDIRS => 10,
//                 CURLOPT_TIMEOUT => 0,
//                 CURLOPT_FOLLOWLOCATION => true,
//                 CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//                 CURLOPT_CUSTOMREQUEST => 'POST',
//                 CURLOPT_POSTFIELDS => '{
//                      "session_id": "' . $session_id . '",
//                      "collection_id": "",
//                      "user_response": "' . $res_data . '",
//                      "response_type": "options",
//                      "followup_yes": "",
//                      "followup_no": "",
//                      "scheme_id": "",
//                      "department_id": "",
//                      "criteria_list": ' . $criteria_list_json . '
//                    }',
//                 CURLOPT_HTTPHEADER => array(
//                     'Content-Type: application/json'
//                 ),
//             ));

//             $response = curl_exec($curl);

//             curl_close($curl);
//             $res = json_decode($response, true);
//             //echo"<pre>";print_r($res);
//             $this->session->set_userdata('intent_name', $res['intent_name']);
//             $this->session->set_userdata('criteria_list', $res['criteria_list']);

//             echo $response;
//             //exit;
//         } else {
//             echo "Empty Session";
//         }
//     }

// }












/*******************************************EoDB chatbot******************** */

    //AI Sanlaap

    public function aiSanlaapEODB()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_eodb_view', $data);
    }

    public function chatFormSubmitEODB()
    {
        //echo"From chatFormSubmit";exit;
        //$name = $this->input->post('name');
        $dob = $this->input->post('dob');
        $gender = $this->input->post('gender');
        $marital_status = $this->input->post('marital_status');
        $social_category = $this->input->post('social_category');
        $income = $this->input->post('income');
        $data_res = "dob:" . $dob . ",gender:" . $gender . ",marital_status:" . $marital_status . ",caste:" . $social_category . ",annual_income:" . $income;
        //echo $data_res;exit;

        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://************:8012/predict',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
             "session_id": "' . $session_id . '",
             "collection_id": "",
             "user_response": "' . $data_res . '",
             "response_type": "form",
             "followup_yes": "",
             "followup_no": "",
             "scheme_id": "",
             "department_id": "",
             "criteria_list":[""]
             }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $res = json_decode($response, true);
        $criteria_list = $res['criteria_list'];
        //echo $criteria_list;exit;
        //echo"<pre>";print_r($res['criteria_list']);exit;
        $this->session->set_userdata('criteria_list', $criteria_list);
        $this->session->set_userdata('intent_name', $res['intent_name']);

        //echo"<pre>";print_r($this->session->userdata('criteria_list'));
        curl_close($curl);
        echo $response;
    }

    public function sessionCreateEODB()
    {

        // $curl = curl_init();

        // curl_setopt_array($curl, array(
        //     CURLOPT_URL => 'http://************:8012/session_id_generator',
        //     CURLOPT_RETURNTRANSFER => true,
        //     CURLOPT_ENCODING => '',
        //     CURLOPT_MAXREDIRS => 10,
        //     CURLOPT_TIMEOUT => 0,
        //     CURLOPT_FOLLOWLOCATION => true,
        //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        //     CURLOPT_CUSTOMREQUEST => 'POST',
        //     /*CURLOPT_POSTFIELDS => '{
        //      "session_id_params": {
        //          "session_type": "session_create",
        //      }
        //      }',*/
        //     CURLOPT_POSTFIELDS => '{
        //          "session_id_params": {
        //            "session_type": "session_create",
        //            "session_id": "NA"
        //          }
        //      }',
        //     CURLOPT_HTTPHEADER => array(
        //         'Content-Type: application/json'
        //     ),
        // ));

        // $response = curl_exec($curl);

        // curl_close($curl);
        /*echo $response;
         exit;*/
         

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://127.0.0.1:8022/session_manager',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
        "session_type": "session_create",
        "session_id": ""
        }
        ',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        //echo $response;

        $res = json_decode($response, true);
        echo "response=" . $response;
        echo "session_id=" . $res['session_id'];
        $this->session->set_userdata('session_id', $res['session_id']);
        //$this->session->set_userdata('followup_no', $res['followup_no']);

    }

    public function sessionDestroyEODB()
    {
        if ($this->session->has_userdata('step')) {
            $this->session->unset_userdata('step');

        }
        if ($this->session->has_userdata('session_flag')) {
            $this->session->unset_userdata('session_flag');

        }
        //echo"destroy";exit;
        //echo"<pre>";print_r($_SESSION);exit;
        $session_id = $this->session->userdata('session_id');
        if ($this->session->has_userdata('session_id')) {
            // $curl = curl_init();

            // curl_setopt_array($curl, array(
            //     CURLOPT_URL => 'http://************:8012/session_id_generator',
            //     CURLOPT_RETURNTRANSFER => true,
            //     CURLOPT_ENCODING => '',
            //     CURLOPT_MAXREDIRS => 10,
            //     CURLOPT_TIMEOUT => 0,
            //     CURLOPT_FOLLOWLOCATION => true,
            //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            //     CURLOPT_CUSTOMREQUEST => 'POST',
            //     CURLOPT_POSTFIELDS => '{
            //      "session_id_params": {
            //        "session_type": "session_destroy",
            //        "session_id": "' . $session_id . '"
            //      }
            //  }',
            //     CURLOPT_HTTPHEADER => array(
            //         'Content-Type: application/json'
            //     ),
            // ));

            // $response = curl_exec($curl);

            // curl_close($curl);
            /*echo $response;
            exit;*/

            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://127.0.0.1:8022/session_manager',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>'{
            "session_type": "session_destroy",
            "session_id": "073641bf-0acd-4cc8-9ebd-c6afb45a61d3"
            }
            ',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            //echo $response;

            $res = json_decode($response, true);

            $this->session->unset_userdata('session_id');

            echo "response=" . $response;
        }
    }

    public function aiSanlaapBotQuestionAnswerEODB()
    {

        $collection_id = "collection_4fcd8a1d_bfa1_42f1_b7c4_b986018efc68";
        $question = $this->input->post('question');
        $language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        $caption="";
        $value="";
        $step_id = "";
        $next_step=1;
        $user_input="Choose sector or industry";
        /*if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
             $followup_yes = $this->session->userdata('followup_yes');
             $followup_no = $this->session->userdata('followup_no');
         }*/
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

            
        if ($this->session->has_userdata('step')) {
            $step_id = $this->session->userdata('step');
            if($step_id==1){
                $caption="sector";
                $value=$question;
                $next_step=2;
                $user_input=$question;
            }
            elseif($step_id==2){
                $caption="investment";
                $value=$question;
                $next_step=3;
                $user_input=$question;
            }
            elseif($step_id==3 && $question=="Do you want to ask general queries?"){
                $caption="application_type";
                $value=$question;
                $next_step=4;
                $user_input="";
            }
            elseif($step_id==3){
                $caption="application_type";
                $value=$question;
                $next_step=4;
                $user_input="";  // Don't send button text as user_input for option selections
            }
            elseif($step_id==4 && $this->session->has_userdata('session_flag') && $question != "Pre-operational" && $question != "Pre-establishment"){
                $caption="general_query";
                $value="";  // Clear value since we're sending the query as user_input
                $next_step=4;  // Keep the same step for follow-up questions
                $user_input=$question;  // Send the actual user query as user_input for AI processing
                // Keep session_flag for follow-up questions - don't unset it immediately
            }
            elseif($step_id==4 && $question=="Pre-operational"){
                $caption="service_type";
                $value=$question;
                $next_step=5;
                $user_input="";  // Don't send button text as user_input for option selections
            }
            elseif($step_id==4 && $question=="Pre-establishment"){
                $caption="service_type";
                $value=$question;
                $next_step=5;
                $user_input="";  // Don't send button text as user_input for option selections
            }
            elseif($step_id==5){
                // After selecting a specific service, move to step 6 for text input
                $caption="selected_service";
                $value=$question;
                $next_step=6;
                $user_input="";
            }
            elseif($step_id==6){
                // Handle service-specific queries with AI
                $caption="service_query";
                $value="";
                $next_step=6; // Stay in step 6 for follow-up questions
                $user_input=$question;
            }
            // Fallback: If user is in step 4 but doesn't match any specific condition, treat as general query
            elseif($step_id==4 && !empty($question)){
                $caption="general_query";
                $value="";
                $next_step=4;
                $user_input=$question;
                // Set session flag to ensure proper handling
                $this->session->set_userdata('session_flag', "Do you want to ask general queries?");
            }
            // Fallback: If user is in step 6 but doesn't match any specific condition, treat as service query
            elseif($step_id==6 && !empty($question)){
                $caption="service_query";
                $value="";
                $next_step=6;
                $user_input=$question;
                // Set session flag for service queries
                $this->session->set_userdata('session_flag', "service_query");
            }
            //elseif($question==""){}
        }

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://127.0.0.1:8022/chatbot/step',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
        "session_id": "'.$session_id.'",
        "collection_name":"collection_4fcd8a1d_bfa1_42f1_b7c4_b986018efc68",
        "user_input": "'.$user_input.'",
        "step": '.$next_step.',
        "user_response": {
            "caption": "'.$caption.'",
            "value": "'.$value.'"
        },
        "response_type": "",
        "followup_yes": "",
        "followup_no": "",
        "criteria_list": []
        }',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $res = json_decode($response, true);
        //$session_step=$res['step'];
        //echo"<pre>";print_r($res);exit;
        if (isset($res['step'])) {
            $this->session->set_userdata('step', $res['step']);
        }
        // Do NOT unset step here, only update if backend provides a new step
        if($res['intent_name']=="ai_response" || $res['intent_name']=="general_query_prompt"){
            $this->session->set_userdata('session_flag', "Do you want to ask general queries?");
        } elseif($res['intent_name']=="service_query_prompt" || $res['intent_name']=="ai_response"){
            // Set flag for service-specific queries
            $this->session->set_userdata('session_flag', "service_query");
        } elseif($res['intent_name']=="choose_sector" || $res['intent_name']=="choose_investment" || $res['intent_name']=="choose_action" || $res['intent_name']=="choose_application_type" || $res['intent_name']=="choose_pre_establishment" || $res['intent_name']=="choose_pre_operation"){
            // Clear the flag only for non-query flows
            $this->session->unset_userdata('session_flag');
        }
        // Keep session_flag for general queries to allow follow-up questions
        echo $response;
    }

    public function schemeBucketEODB()
    {
        $question = $this->input->post('scheme_bucket');
        $intent_name = $this->session->userdata('intent_name');
        //echo "intent_name=".$intent_name;
        //echo "question=".$question;
        $res_data = $intent_name . ":" . $question;
        //echo "res_data=".$res_data;
        //echo "criteria_list=".json_encode($this->session->userdata('criteria_list'));
        //$res_data = "scheme_bucket:".$question;
        //echo "scheme_bucket=".$res_data;
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
            $criteria_list = $this->session->userdata('criteria_list');
            $criteria_list_json = json_encode($criteria_list);

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http:// 172.18.97.170:8020/chatbot/step',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                     "session_id": "' . $session_id . '",
                     "collection_id": "",
                     "user_response": "' . $res_data . '",
                     "response_type": "options",
                     "followup_yes": "",
                     "followup_no": "",
                     "scheme_id": "",
                     "department_id": "",
                     "criteria_list": ' . $criteria_list_json . '
                   }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $res = json_decode($response, true);
            //echo"<pre>";print_r($res);
            $this->session->set_userdata('intent_name', $res['intent_name']);
            $this->session->set_userdata('criteria_list', $res['criteria_list']);

            echo $response;
            //exit;
        } else {
            echo "Empty Session";
        }
    }

}
