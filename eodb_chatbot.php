/*******************************************EoDB chatbot******************** */

    //AI Sanlaap

    public function aiSanlaapEODB()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;
        $this->load->view('super_admin/sanlaap_bot_eodb_view', $data);
    }

    public function chatFormSubmitEODB()
    {
        //echo"From chatFormSubmit";exit;
        //$name = $this->input->post('name');
        $dob = $this->input->post('dob');
        $gender = $this->input->post('gender');
        $marital_status = $this->input->post('marital_status');
        $social_category = $this->input->post('social_category');
        $income = $this->input->post('income');
        $data_res = "dob:" . $dob . ",gender:" . $gender . ",marital_status:" . $marital_status . ",caste:" . $social_category . ",annual_income:" . $income;
        //echo $data_res;exit;

        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://10.173.17.19:8012/predict',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
             "session_id": "' . $session_id . '",
             "collection_id": "",
             "user_response": "' . $data_res . '",
             "response_type": "form",
             "followup_yes": "",
             "followup_no": "",
             "scheme_id": "",
             "department_id": "",
             "criteria_list":[""]
             }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $res = json_decode($response, true);
        $criteria_list = $res['criteria_list'];
        //echo $criteria_list;exit;
        //echo"<pre>";print_r($res['criteria_list']);exit;
        $this->session->set_userdata('criteria_list', $criteria_list);
        $this->session->set_userdata('intent_name', $res['intent_name']);

        //echo"<pre>";print_r($this->session->userdata('criteria_list'));
        curl_close($curl);
        echo $response;
    }

    public function sessionCreateEODB()
    {

        // $curl = curl_init();

        // curl_setopt_array($curl, array(
        //     CURLOPT_URL => 'http://10.173.17.19:8012/session_id_generator',
        //     CURLOPT_RETURNTRANSFER => true,
        //     CURLOPT_ENCODING => '',
        //     CURLOPT_MAXREDIRS => 10,
        //     CURLOPT_TIMEOUT => 0,
        //     CURLOPT_FOLLOWLOCATION => true,
        //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        //     CURLOPT_CUSTOMREQUEST => 'POST',
        //     /*CURLOPT_POSTFIELDS => '{
        //      "session_id_params": {
        //          "session_type": "session_create",
        //      }
        //      }',*/
        //     CURLOPT_POSTFIELDS => '{
        //          "session_id_params": {
        //            "session_type": "session_create",
        //            "session_id": "NA"
        //          }
        //      }',
        //     CURLOPT_HTTPHEADER => array(
        //         'Content-Type: application/json'
        //     ),
        // ));

        // $response = curl_exec($curl);

        // curl_close($curl);
        /*echo $response;
         exit;*/
         

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://127.0.0.1:8022/session_manager',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
        "session_type": "session_create",
        "session_id": ""
        }
        ',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        //echo $response;

        $res = json_decode($response, true);
        echo "response=" . $response;
        echo "session_id=" . $res['session_id'];
        $this->session->set_userdata('session_id', $res['session_id']);
        //$this->session->set_userdata('followup_no', $res['followup_no']);

    }

    public function sessionDestroyEODB()
    {
        if ($this->session->has_userdata('step')) {
            $this->session->unset_userdata('step');

        }
        if ($this->session->has_userdata('session_flag')) {
            $this->session->unset_userdata('session_flag');

        }
        //echo"destroy";exit;
        //echo"<pre>";print_r($_SESSION);exit;
        $session_id = $this->session->userdata('session_id');
        if ($this->session->has_userdata('session_id')) {
            // $curl = curl_init();

            // curl_setopt_array($curl, array(
            //     CURLOPT_URL => 'http://10.173.17.19:8012/session_id_generator',
            //     CURLOPT_RETURNTRANSFER => true,
            //     CURLOPT_ENCODING => '',
            //     CURLOPT_MAXREDIRS => 10,
            //     CURLOPT_TIMEOUT => 0,
            //     CURLOPT_FOLLOWLOCATION => true,
            //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            //     CURLOPT_CUSTOMREQUEST => 'POST',
            //     CURLOPT_POSTFIELDS => '{
            //      "session_id_params": {
            //        "session_type": "session_destroy",
            //        "session_id": "' . $session_id . '"
            //      }
            //  }',
            //     CURLOPT_HTTPHEADER => array(
            //         'Content-Type: application/json'
            //     ),
            // ));

            // $response = curl_exec($curl);

            // curl_close($curl);
            /*echo $response;
            exit;*/

            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://127.0.0.1:8022/session_manager',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>'{
            "session_type": "session_destroy",
            "session_id": "073641bf-0acd-4cc8-9ebd-c6afb45a61d3"
            }
            ',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            //echo $response;

            $res = json_decode($response, true);

            $this->session->unset_userdata('session_id');

            echo "response=" . $response;
        }
    }

    public function aiSanlaapBotQuestionAnswerEODB()
    {

        $collection_id = "collection_4fcd8a1d_bfa1_42f1_b7c4_b986018efc68";
        $question = $this->input->post('question');
        $language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        $caption="";
        $value="";
        $step_id = "";
        $next_step=1;
        $user_input="Choose sector or industry";
        /*if ($this->session->has_userdata('followup_yes') && $this->session->has_userdata('followup_no')) {
             $followup_yes = $this->session->userdata('followup_yes');
             $followup_no = $this->session->userdata('followup_no');
         }*/
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

            
        if ($this->session->has_userdata('step')) {
            $step_id = $this->session->userdata('step');
            if($step_id==1){
                $caption="sector";
                $value=$question;
                $next_step=2;
                $user_input=$question;
            }
            elseif($step_id==2){
                $caption="investment";
                $value=$question;
                $next_step=3;
                $user_input=$question;
            }
            elseif($step_id==3 && $question=="Do you want to ask general queries?"){
                $caption="application_type";
                $value=$question;
                $next_step=4;
                $user_input="";  // This will trigger the text input prompt
            }
            elseif($step_id==3){
                $caption="application_type";
                $value=$question;
                $next_step=4;
                $user_input=$question;
            }
            elseif($step_id==4 && $this->session->has_userdata('session_flag') && $question != "Pre-operational" && $question != "Pre-establishment"){
                $caption="general_query";
                $value="";  // Clear value since we're sending the query as user_input
                $next_step=4;  // Keep the same step
                $user_input=$question;  // Send the actual user query as user_input
                $this->session->unset_userdata('session_flag');  // Unset the session flag
            }
            elseif($step_id==4 && $question=="Pre-operational"){
                $caption="service_type";
                $value=$question;
                $next_step=5;
                $user_input=$question;
            }
            elseif($step_id==4 && $question=="Pre-establishment"){
                $caption="service_type";
                $value=$question;
                $next_step=5;
                $user_input=$question;
            }
            elseif($step_id==5){
                echo"done";exit;
            }
            //elseif($question==""){}
        }

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://127.0.0.1:8022/chatbot/step',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
        "session_id": "'.$session_id.'",
        "collection_name":"collection_4fcd8a1d_bfa1_42f1_b7c4_b986018efc68",
        "user_input": "'.$user_input.'",
        "step": '.$next_step.',
        "user_response": {
            "caption": "'.$caption.'",
            "value": "'.$value.'"
        },
        "response_type": "",
        "followup_yes": "",
        "followup_no": "",
        "criteria_list": []
        }',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $res = json_decode($response, true);
        //$session_step=$res['step'];
        //echo"<pre>";print_r($res);exit;
        if (isset($res['step'])) {
            $this->session->set_userdata('step', $res['step']);
        }
        // Do NOT unset step here, only update if backend provides a new step
        if($res['intent_name']=="ai_response"){
            $this->session->set_userdata('session_flag', "Do you want to ask general queries?");
        } else {
            $this->session->unset_userdata('session_flag');  // Clear the flag for other intents
        }
        echo $response;
    }

    public function schemeBucketEODB()
    {
        $question = $this->input->post('scheme_bucket');
        $intent_name = $this->session->userdata('intent_name');
        //echo "intent_name=".$intent_name;
        //echo "question=".$question;
        $res_data = $intent_name . ":" . $question;
        //echo "res_data=".$res_data;
        //echo "criteria_list=".json_encode($this->session->userdata('criteria_list'));
        //$res_data = "scheme_bucket:".$question;
        //echo "scheme_bucket=".$res_data;
        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
            $criteria_list = $this->session->userdata('criteria_list');
            $criteria_list_json = json_encode($criteria_list);

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http:// 172.18.97.170:8020/chatbot/step',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                     "session_id": "' . $session_id . '",
                     "collection_id": "",
                     "user_response": "' . $res_data . '",
                     "response_type": "options",
                     "followup_yes": "",
                     "followup_no": "",
                     "scheme_id": "",
                     "department_id": "",
                     "criteria_list": ' . $criteria_list_json . '
                   }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $res = json_decode($response, true);
            //echo"<pre>";print_r($res);
            $this->session->set_userdata('intent_name', $res['intent_name']);
            $this->session->set_userdata('criteria_list', $res['criteria_list']);

            echo $response;
            //exit;
        } else {
            echo "Empty Session";
        }
    }

}
