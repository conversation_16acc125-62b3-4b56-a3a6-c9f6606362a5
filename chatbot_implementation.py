"""
Chatbot implementation based on <PERSON><PERSON><PERSON><PERSON> vector database for semantic search.
This module provides functions for creating collections, loading data, and querying the chatbot.
"""
import uuid
import re
import pandas as pd
import numpy as np
from pymilvus import (
    connections,
    Collection,
    CollectionSchema,
    FieldSchema,
    DataType,
    utility
)
from sentence_transformers import SentenceTransformer
import logging
from fuzzywuzzy import process
import nltk
from nltk.corpus import words
from nltk.stem import WordNetLemmatizer
import spacy
from nltk.sentiment.vader import SentimentIntensityAnalyzer

# --- Setup logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Set NLTK data path to use manually downloaded data ---
# nltk_data_path = '/home/<USER>/nltk_data'
# nltk.data.path.insert(0, nltk_data_path)  # Add the custom path at the beginning of the search path
# logger.info(f"Using NLTK data from: {nltk_data_path}")

# # Verify that NLTK can find the required resources
# try:
#     nltk.data.find('corpora/words')
#     nltk.data.find('corpora/wordnet')
#     nltk.data.find('sentiment/vader_lexicon')
#     logger.info("All required NLTK resources found successfully")
# except LookupError as e:
#     logger.warning(f"Could not find some NLTK resources: {e}")
#     logger.warning("Some chatbot features may not work correctly")

# --- Load NLP tools ---
try:
    nlp = spacy.load('en_core_web_sm')
except OSError:
    import subprocess
    import sys
    subprocess.run([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])
    nlp = spacy.load('en_core_web_sm')

lemmatizer = WordNetLemmatizer()
word_list = set(words.words())
sentiment_analyzer = SentimentIntensityAnalyzer()

# --- Load embedding model globally ---
model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")

# --- Constants ---
THRESHOLD = 0.65  # Similarity threshold for responses

# --- Connect to Milvus ---
def connect_to_milvus(host="localhost", port="19530"):
    """Connect to Milvus server"""
    connections.connect(host=host, port=port)
    logger.info(f"Connected to Milvus server at {host}:{port}")

# --- Create unique collection ---
def create_unique_collection():
    """Generate a unique collection name and return it"""
    collection_name = f"collection_{str(uuid.uuid4()).replace('-', '_')}"
    collection_name_sanitized = re.sub(r'[^a-zA-Z0-9_]', '_', collection_name)
    logger.info(f"Generated unique collection name: {collection_name_sanitized}")
    return collection_name_sanitized

# --- Define schema ---
def define_schema():
    """Define schema for Milvus collection"""
    fields = [
        FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=False, description="Intent ID"),
        FieldSchema(name="intent_name", dtype=DataType.VARCHAR, max_length=100, description="Name of the intent"),
        FieldSchema(name="intent_related_text", dtype=DataType.FLOAT_VECTOR, dim=384, description="Intent related text"),
        FieldSchema(name="response", dtype=DataType.VARCHAR, max_length=4200, description="Response text"),
        FieldSchema(name="followup_yes", dtype=DataType.VARCHAR, max_length=555, nullable=True, description="Follow-up intent for 'yes'"),
        FieldSchema(name="followup_no", dtype=DataType.VARCHAR, max_length=555, nullable=True, description="Follow-up intent for 'no'"),
        FieldSchema(name="intent_id", dtype=DataType.VARCHAR, max_length=100, nullable=True, description="Intent ID (can be text or number)")
    ]
    schema = CollectionSchema(fields=fields)
    logger.info("Schema defined successfully")
    return schema
# --- Generate embeddings ---
def generate_embeddings(text, model):
    """Generate embeddings for the input text"""
    embedding = model.encode(text)
    return embedding

# --- Load CSV knowledge base ---
def load_csv_knowledge_base(csv_path):
    """Load data from CSV file"""
    try:
        data = pd.read_csv(csv_path)
        logger.info(f"Loaded CSV with {len(data)} rows from {csv_path}")
        return data
    except Exception as e:
        logger.error(f"Error loading CSV: {e}")
        return None

# --- Process and clean data ---
def process_and_clean_data(data):
    """Process and clean the data, generate embeddings"""
    # Convert columns to string type first
    data['followup_yes'] = data['followup_yes'].astype(str)
    data['followup_no'] = data['followup_no'].astype(str)
    data['intent_id'] = data['intent_id'].astype(str)

    for index, row in data.iterrows():
        data.at[index, 'intent_related_text'] = generate_embeddings(row['intent_related_text'], model)
        data.at[index, 'followup_yes'] = "" if pd.isna(row['followup_yes']) else row['followup_yes']
        data.at[index, 'followup_no'] = "" if pd.isna(row['followup_no']) else row['followup_no']
        data.at[index, 'intent_id'] = "" if pd.isna(row['intent_id']) else str(row['intent_id'])
    logger.info("Data processed and cleaned successfully")
    return data

# --- Insert data into Milvus ---
def insert_data_into_milvus(collection, data):
    """Insert processed data into Milvus collection"""
    try:
        records = data.to_dict('list')
        insert_data = [
            records['id'],
            records['intent_name'],
            records['intent_related_text'],
            records['response'],
            records['followup_yes'],
            records['followup_no'],
            records['intent_id']
        ]
        collection.insert(insert_data)

        # Create index
        index_params = {"index_type": "IVF_FLAT", "metric_type": "COSINE", "params": {"nlist": 128}}
        collection.create_index("intent_related_text", index_params)
        collection.load()
        logger.info(f"Inserted {len(data)} records into Milvus collection")
        return True
    except Exception as e:
        logger.error(f"Error inserting data into Milvus: {e}")
        return False

# --- Create collection and load data ---
def create_collection_and_load_data(csv_path):
    """Create a new collection, process data, and load it into Milvus"""
    connect_to_milvus()
    collection_name = create_unique_collection()
    schema = define_schema()

    try:
        collection = Collection(name=collection_name, schema=schema)
        logger.info(f"Collection '{collection_name}' created successfully")

        data = load_csv_knowledge_base(csv_path)

        if data is not None:
            processed_data = process_and_clean_data(data)
            success = insert_data_into_milvus(collection, processed_data)
            if success:
                return collection_name
    except Exception as e:
        logger.error(f"Error creating collection and loading data: {e}")

    return None

# --- Get collections ---
def get_collections():
    """Fetch available collections from Milvus"""
    collections = utility.list_collections()
    collection_dict = {name: name for name in collections}
    return collection_dict

# --- Get the most recently created collection ---
# def get_latest_collection():
#     """Get the most recently created collection from Milvus"""
#     collections = utility.list_collections()
#     if not collections:
#         return None

#     # Log all available collections
#     logger.info(f"All available collections: {collections}")

#     # For UUID-based collection names, we can sort them to get the most recent
#     # Since UUIDs are time-based, the most recent one will be last alphabetically
#     sorted_collections = sorted(collections)
#     logger.info(f"Sorted collections: {sorted_collections}")

#     # Get the last collection (most recently created)
#     latest_collection = sorted_collections[-1]
#     logger.info(f"Selected latest collection: {latest_collection}")
#     return latest_collection

# --- Preprocess text ---
def preprocess_text(text):
    """Preprocess text for NLP tasks"""
    text = text.lower()
    doc = nlp(text)
    return ' '.join([token.lemma_ for token in doc])

# --- Check if text is gibberish ---
def is_gibberish_with_nltk(text):
    """Check if the input text is gibberish"""
    # Define a custom list of valid words for Silpasathi
    custom_valid_words = {"silpasathi", "sp", "silpasathi prakalap", "silpa", "sathi", "silpa sathi",
                         "silpasathi prakalpa", "silpasathi portal", "silpa sathi portal", "silpa sathi prakalpa"}

    # Remove non-alphabetic characters from the text
    clean_text = re.sub(r'[^a-zA-Z\s]', '', text)
    words_in_text = clean_text.split()

    # If the input is empty or invalid, consider it gibberish
    if not words_in_text:
        return True

    # Count valid words from NLTK word list and custom list
    valid_words = sum(1 for word in words_in_text if word.lower() in word_list or word.lower() in custom_valid_words)
    total_words = len(words_in_text)

    # Calculate the ratio of valid words
    gibberish_ratio = valid_words / total_words if total_words > 0 else 0
    THRESHOLD = 0.5  # Adjust threshold as needed

    return gibberish_ratio < THRESHOLD

# --- Main chatbot response function ---
def chatbot_response(req, collection_name, followup_yes='', followup_no=''):
    """Main chatbot response function"""
    # Predefined responses for common greetings
    predefined_responses = {
        "hi": "Namaskar🙏🏻! How can I assist you today?",
        "hello": "Namaskar🙏🏻! How can I assist you today?",
        "thanks": "You're welcome! If you have any other questions, feel free to ask.",
        "thank you": "You're welcome! If you have any other questions, feel free to ask.",
        "who are you": "Hi, I am AI Sanlaap, a chatbot developed by CoE-AI Lab Kolkata, NIC WBSC."
    }

    # Handle single-word queries
    single_word_queries = {"how", "what", "why", "when", "where", "who", "about", "they", "them",
                          "this", "that", "in", "on", "are", "was", "have", "has", "should",
                          "had", "could", "i love", "love", "bad"}
    req_cleaned = re.sub(r'[^A-Za-z\s]', '', req.lower()).strip()

    if req_cleaned in single_word_queries:
        return {
            "id": '',
            "response": "Could you please provide more details? I'm here to assist you!",
            "followup_yes": '',
            "followup_no": '',
            "score": '',
            "intent_id": ''
        }

    # Check if input is gibberish
    if is_gibberish_with_nltk(req):
        return {
            "id": '',
            'response': "Sorry, I apologize 😊, but please inquire if you need anything else. I'm glad to assist.",
            "followup_yes": '',
            "followup_no": '',
            "score": '',
            "intent_id": ''
        }

    # Check for predefined responses
    # req_cleaned = re.sub(r'[^A-Za-z\s]', '', req.lower()).strip()
    # best_match, score = process.extractOne(req_cleaned, predefined_responses.keys())
    # if score >= 85:
    #     return {
    #         "id": '',
    #         'response': predefined_responses[best_match],
    #         "followup_yes": '',
    #         "followup_no": '',
    #         "score": '',
    #         "intent_id": ''
    #     }

    # Process the request for vector search
    # model = model()
    req_preprocessed = preprocess_text(req_cleaned)
    req_embeddings = generate_embeddings(req_preprocessed, model)

    # try:
    collection = Collection(collection_name)

        # Create index (will be ignored if it already exists)
    #     try:
    #         logger.info(f"Creating index for collection {collection_name}")
    #         index_params = {"index_type": "IVF_FLAT", "metric_type": "COSINE", "params": {"nlist": 128}}
    #         collection.create_index("intent_related_text", index_params)
    #         logger.info(f"Index created successfully for {collection_name}")
    #     except Exception as e:
    #         if "index already exists" in str(e).lower():
    #             logger.info(f"Index already exists for collection {collection_name}")
    #         else:
    #             logger.warning(f"Error creating index: {e}")

    #     # Load collection
    #     collection.load()
    # except Exception as e:
    #     logger.error(f"Error loading collection {collection_name}: {e}")
    #     return {
    #         "id": '',
    #         "response": "Sorry, I'm having trouble accessing my knowledge base. Please try again later.",
    #         "followup_yes": '',
    #         "followup_no": '',
    #         "score": '',
    #         "intent_id": ''
    #     }
    # collection_name = get_collections(collection_name)
    # if not collection_name:
    #     return {"response": f"Unknown collection_name: {collection_name}", "followup_intent": None}

# /    collection = Collection(collection_name)
    collection.load()
    # Search in Milvus
    # try:
    #     results = collection.search(
    #         data=[req_embeddings.tolist()],
    #         anns_field="intent_related_text",
    #         param={"metric_type": "COSINE", "params": {"nlist": 128}},
    #         limit=10,
    #         output_fields=["id", "intent_name", "response", "followup_yes", "followup_no", "intent_id"]
    #     )
    #     thresold : 0.75
    #     # Debug log to see what results we're getting
    #     if results and len(results[0]) > 0:
    #         logger.info(f"Query: '{req}' - Top result score: {results[0][0].score}")
    #         logger.info(f"Top result response: {results[0][0].entity.get('response')[:100]}...")
    #         logger.info(f"Threshold: {thresold}")
    #     else:
    #         logger.info(f"Query: '{req}' - No results found")

    #     if not results or len(results[0]) == 0:
    #         return {
    #             "id": '',
    #             "response": "Thank you for your query! Could you please clarify or provide more details so I can assist you effectively?",
    #             "followup_yes": '',
    #             "followup_no": '',
    #             "score": '',
    #             "intent_id": ''
    #         }

    #     top_result = results[0][0]

    #     # Check if the result meets the threshold
    #     if top_result.score >= thresold:
    #         followup_yes = top_result.entity.get("followup_yes")
    #         followup_no = top_result.entity.get("followup_no")

    #         logger.info(f"Query: '{req}' - Found match above threshold ({top_result.score} >= {thresold})")

    #         return {
    #             "id": top_result.id,
    #             "response": top_result.entity.get("response"),
    #             "followup_yes": followup_yes,
    #             "followup_no": followup_no,
    #             "score": top_result.score,
    #             "intent_id": top_result.entity.get("intent_id")
    #         }
    #     else:
    #         logger.info(f"Query: '{req}' - Score below threshold ({top_result.score} < {thresold})")

    #         # Return the best match anyway, but with a note that it's below the confidence threshold
    #         # return {
    #         #     "id": top_result.id,
    #         #     "response": top_result.entity.get("response"),
    #         #     "followup_yes": top_result.entity.get("followup_yes"),
    #         #     "followup_no": top_result.entity.get("followup_no"),
    #         #     "score": top_result.score,
    #         #     "intent_id": top_result.entity.get("intent_id")
    #         # }
    #         return {
    #             "id": '',
    #             "response": "Thank you for your query! Could you please clarify or provide more details so I can assist you effectively?",
    #             "followup_yes": '',
    #             "followup_no": '',
    #             "score": '', 
    #             "intent_id": ''
    #         }
    # except Exception as e:
    #     logger.error(f"Error searching in collection: {e}")
    #     return {
    #         "id": '',
    #         "response": "Sorry, I encountered an error while processing your request. Please try again.",
    #         "followup_yes": '',
    #         "followup_no": '',
    #         "score": '',
    #         "intent_id": ''
    #     }
    results = collection.search(
            data=[req_embeddings.tolist()],
            anns_field="intent_related_text",
            param={"metric_type": "COSINE", "params": {"nlist": 128}},
            limit=10,
            output_fields=["id", "intent_name", "response", "followup_yes", "followup_no","intent_id"]
        )
    # except Exception as e:
    #     logging.error(f"Milvus search failed: {e}", exc_info=True)
    #     return {"response": "Sorry, something went wrong. Please try again later.", "followup_intent": None}

    THRESHOLD = 0.65  # Lowered to allow more relevant matches from knowledge base

    #adaptive_threshold = get_adaptive_threshold(collection_name)

   # if results and len(results[0]) > 0:
        #top_result = results[0][0]

        # Store the retrieved score in history
        # historical_scores[collection_name].append(top_result.score)
        #if len(historical_scores[collection_name]) > 100:  # Limit history size
          #  historical_scores[collection_name].pop(0)
    if results and len(results[0]) > 0:
        if followup_yes != '' or followup_no != '':
            pos_res_score = neg_res_score = 0
            for res in results[0]:
                print(res.entity.get("id"))
                print(res.score)
                if res.entity.get("intent_name") == followup_yes and pos_res_score == 0:
                    pos_res_score = res.score
                    print("pos sc",pos_res_score)
                    pos_res = res
                if res.entity.get("intent_name") == followup_no and neg_res_score == 0:
                    neg_res_score = res.score
                    neg_res = res
                    print("neg sc",neg_res_score)
            try:
                if pos_res_score > neg_res_score:
                    # intent insertion
                    #pos_res.entity.get("intent_name")
                    # chat_stat(operation='insert', values={'intent_id': pos_res.entity.get("intent_name"),
                    #                                       'question': req.lower().strip() 
                    #                                         })
                    return {
                        "id": pos_res.id,
                        "intent_name" : pos_res.entity.get("intent_name"),
                        "response": pos_res.entity.get("response"),
                        "followup_yes": pos_res.entity.get("followup_yes"),
                        "followup_no": pos_res.entity.get("followup_no"),
                        "score": pos_res_score,
                        "intent_id":pos_res.entity.get("intent_id")
                    }
                else:
                    # intent insertion
                    # chat_stat(operation='insert', values={'intent_id': neg_res.entity.get("intent_name"),
                    #                                       'question': req.lower().strip() 
                    #                                         })
                    return {
                        "id": neg_res.id,
                        "intent_name" : neg_res.entity.get("intent_name"),
                        "response": neg_res.entity.get("response"),
                        "followup_yes": neg_res.entity.get("followup_yes"),
                        "followup_no": neg_res.entity.get("followup_no"),
                        "score": neg_res_score,
                        "intent_id":neg_res.entity.get("intent_id")
                    }
            except:
                # intent insertion
                # chat_stat(operation='insert', values={'intent_id': results[0][0].entity.get("intent_name"),
                #                                       'question': req.lower().strip() 
                #                                         })
                return {
                        "id": results[0][0].id,
                        "intent_name" : results[0][0].entity.get("intent_name"),
                        "response": results[0][0].entity.get("response"),
                        "followup_yes": results[0][0].entity.get("followup_yes"),
                        "followup_no": results[0][0].entity.get("followup_no"),
                        "score": results[0][0].score,
                        "intent_id":results[0][0].entity.get("intent_id")
                    }

        else:    
            top_result = results[0][0]
            print(top_result)
            if top_result.score >= THRESHOLD and top_result.entity.get("followup_yes") != ''  and top_result.entity.get("followup_no") != '':
                # Check follow-up intent fields
                followup_yes = top_result.entity.get("followup_yes")
                followup_no = top_result.entity.get("followup_no")
                # intent_name = followup_yes if followup_yes else followup_no
                print('.............')
                # intent insertion
                # chat_stat(operation='insert', values={'intent_id': top_result.entity.get("intent_name"),
                #                                       'question': req.lower().strip() 
                #                                         })
                return {
                    "id": top_result.id,
                    "intent_name" :top_result.entity.get("intent_name"),
                    "response": top_result.entity.get("response"),
                    "followup_yes": followup_yes,
                    "followup_no": followup_no,
                    "score": top_result.score,
                    "intent_id":top_result.entity.get("intent_id")

                }
            elif top_result.score >= THRESHOLD:
                followup_yes = top_result.entity.get("followup_yes")
                followup_no = top_result.entity.get("followup_no")
                # intent_name = followup_yes if followup_yes else followup_no
                # intent insertion
                # chat_stat(operation='insert', values={'intent_id': top_result.entity.get("intent_name"),
                #                                       'question': req.lower().strip() 
                #                                         })
                return {
                    "id": top_result.id,
                    "intent_name" :top_result.entity.get("intent_name"),
                    "response": top_result.entity.get("response"),
                    "followup_yes": followup_yes,
                    "followup_no": followup_no,
                    "score": top_result.score,
                    "intent_id":top_result.entity.get("intent_id")
                }
            # else:
            #     req_cleaned = re.sub(r'[^A-Za-z\s]', '', req.lower()).strip()
            #     best_match, score = process.extractOne(req_cleaned, predefined_responses.keys())
            #     if score >= 85:
            #         return {
            #             "id": '',
            #             'response': predefined_responses[best_match],
            #             "followup_yes": '',
            #             "followup_no": '',
            #             "score": '',
            #             "intent_id": ''
            #         }
                # intent insertion
                # chat_stat(operation='insert', values={'intent_id': 'fallback',
                #                                       'question': req.lower().strip() 
                #  
                #                                        })
            else:
                return {
                        "id": '',
                        "intent_name" :'fallback',  
                        "response": "Thank you for your query! Could you please clarify or provide more details so that I can assist you effectively?",
                        "followup_yes": '',
                        "followup_no": '',
                        "score": '',
                        "intent_id":'103'
                    }
    # If no relevant match is found
    else:    
        # intent insertion
        # chat_stat(operation='insert', values={'intent_id': 'fallback',
        #                                       'question': req.lower().strip() 
        #                                         })
        return {
            "id": '',
            "intent_name" :'fallback',  
            "response": "Thank you for your query! Could you please clarify or provide more details so that I can assist you effectively?",
            "followup_yes": '',
            "followup_no": '',
            "score": '',
            "intent_id":'103'
        }